#!/usr/bin/env python3
"""
测试改进后的绘图器
================

验证以下改进：
1. 全屏显示功能
2. 正确的日期hover标签
3. 自定义hover信息
"""

from lB_BT_Plotly import BacktestSystem, BacktestPlotter
from datetime import datetime


def test_fullscreen_plotter():
    """
    测试全屏绘图器
    """
    print("=" * 60)
    print("测试全屏绘图器")
    print("=" * 60)
    
    # 创建全屏绘图器
    fullscreen_plotter = BacktestPlotter(
        figsize=(1600, 1000),  # 大尺寸
        theme='plotly_white',
        fullscreen=True        # 启用全屏
    )
    
    # 创建回测系统
    backtest_system = BacktestSystem(plotter=fullscreen_plotter)
    
    # 设置回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 6, 1)
    end_date = datetime(2023, 12, 31)
    initial_cash = 100000
    
    print(f"运行回测: {symbol}")
    print(f"时间范围: {start_date.date()} 到 {end_date.date()}")
    print("绘图器配置:")
    print("- 全屏显示: 启用")
    print("- 自定义hover标签: 启用")
    print("- 日期显示: 完整日期格式")
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        print(f"\n生成全屏图表...")
        fig = backtest_system.plot_results(symbol)
        
        if fig:
            # 检查图表配置
            print(f"图表配置检查:")
            print(f"- autosize: {fig.layout.autosize}")
            print(f"- width: {fig.layout.width}")
            print(f"- height: {fig.layout.height}")
            
            # 显示图表
            fig.show()
            
            print(f"\n✅ 全屏图表已显示！")
            print("请验证以下功能：")
            print("1. 图表是否占据了整个浏览器窗口")
            print("2. 将鼠标悬停在K线上，是否显示具体日期")
            print("3. 将鼠标悬停在买卖信号点上，是否显示日期和价格")
            print("4. 将鼠标悬停在MACD线上，是否显示日期和数值")
            
        else:
            print("❌ 图表生成失败")
    else:
        print("❌ 回测失败")


def test_non_fullscreen_plotter():
    """
    测试非全屏绘图器（对比）
    """
    print("\n" + "=" * 60)
    print("测试非全屏绘图器（对比）")
    print("=" * 60)
    
    # 创建非全屏绘图器
    normal_plotter = BacktestPlotter(
        figsize=(1200, 800),   # 固定尺寸
        theme='plotly_white',
        fullscreen=False       # 禁用全屏
    )
    
    # 创建回测系统
    backtest_system = BacktestSystem(plotter=normal_plotter)
    
    # 使用相同的回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 11, 1)
    end_date = datetime(2023, 12, 31)
    initial_cash = 50000
    
    print(f"运行对比回测: {symbol}")
    print("绘图器配置:")
    print("- 全屏显示: 禁用")
    print("- 固定尺寸: 1200x800")
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        print(f"\n生成固定尺寸图表...")
        fig = backtest_system.plot_results(symbol)
        
        if fig:
            # 检查图表配置
            print(f"图表配置检查:")
            print(f"- autosize: {fig.layout.autosize}")
            print(f"- width: {fig.layout.width}")
            print(f"- height: {fig.layout.height}")
            
            # 显示图表
            fig.show()
            
            print(f"\n✅ 固定尺寸图表已显示！")
            print("对比两个图表的大小差异")
            
        else:
            print("❌ 图表生成失败")
    else:
        print("❌ 回测失败")


def demonstrate_hover_features():
    """
    演示hover功能特性
    """
    print("\n" + "=" * 60)
    print("Hover功能特性说明")
    print("=" * 60)
    
    print("改进前的问题:")
    print("- 鼠标悬停显示的是bar线编号（如168、167）")
    print("- 无法直观看到具体的交易日期")
    print("- 信息不够详细")
    
    print("\n改进后的功能:")
    print("1. K线图hover信息:")
    print("   - 显示完整日期（如2023-12-15）")
    print("   - 显示开高低收价格")
    print("   - 显示成交量")
    
    print("\n2. 买卖信号hover信息:")
    print("   - 显示信号类型（买入/卖出）")
    print("   - 显示具体日期")
    print("   - 显示信号价格")
    
    print("\n3. MACD指标hover信息:")
    print("   - 显示日期")
    print("   - 显示MACD值和信号线值")
    print("   - 显示直方图值")
    
    print("\n4. 全屏显示:")
    print("   - 图表自动适应浏览器窗口大小")
    print("   - 最大化图表显示区域")
    print("   - 减少边距，增加有效显示面积")


if __name__ == "__main__":
    print("绘图器改进测试")
    print("=" * 60)
    print("本测试验证以下改进：")
    print("1. 全屏显示功能")
    print("2. 正确的日期hover标签")
    print("3. 详细的hover信息")
    print("=" * 60)
    
    # 演示hover功能特性
    demonstrate_hover_features()
    
    # 测试全屏绘图器
    test_fullscreen_plotter()
    
    # 测试非全屏绘图器（对比）
    test_non_fullscreen_plotter()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("请在浏览器中验证以下改进：")
    print("✅ 图表是否占据整个屏幕")
    print("✅ hover标签是否显示正确的日期")
    print("✅ hover信息是否详细和准确")
    print("=" * 60)
