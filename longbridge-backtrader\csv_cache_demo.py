"""
CSV缓存功能演示脚本
==================

演示如何使用CSV格式的缓存功能来保存和使用LongBridge数据。
CSV格式的优势是可以直接用Excel、记事本等工具查看和编辑。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

from lB_BT_Plotly import BacktestSystem, LongBridgeData
from datetime import datetime
import os
import pandas as pd

def demo_csv_cache_basic():
    """演示CSV缓存基本功能"""
    print("\n" + "="*60)
    print("CSV缓存基本功能演示")
    print("="*60)
    
    # 创建启用CSV缓存的回测系统
    system = BacktestSystem(enable_cache=True, cache_dir="csv_cache_demo")
    
    # 测试参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 31)
    
    print(f"\n1. 首次下载 {symbol} 数据（会保存为CSV）")
    print(f"   时间范围: {start_date.date()} 到 {end_date.date()}")
    
    # 首次运行会下载并缓存数据
    results1 = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=100000
    )
    
    if results1:
        print(f"✓ 首次回测完成")
        
        # 查看缓存目录
        cache_dir = "csv_cache_demo"
        if os.path.exists(cache_dir):
            csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
            if csv_files:
                csv_file = os.path.join(cache_dir, csv_files[0])
                print(f"\n📁 CSV缓存文件已创建: {csv_file}")
                
                # 显示CSV文件内容预览
                print("\n--- CSV文件内容预览 ---")
                df = pd.read_csv(csv_file)
                print(f"文件大小: {os.path.getsize(csv_file)} 字节")
                print(f"数据行数: {len(df)} 行")
                print(f"数据列: {list(df.columns)}")
                print("\n前5行数据:")
                print(df.head().to_string(index=False))
                
                print(f"\n💡 您可以用Excel或记事本打开文件查看: {csv_file}")
        
        print(f"\n2. 再次运行相同回测（使用CSV缓存）")
        results2 = system.run_backtest(
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            initial_cash=100000
        )
        
        if results2:
            print(f"✓ 第二次回测完成（使用了CSV缓存数据）")
    
    return system

def demo_csv_cache_management():
    """演示CSV缓存管理功能"""
    print("\n" + "="*60)
    print("CSV缓存管理功能演示")
    print("="*60)
    
    # 使用之前创建的系统
    system = BacktestSystem(enable_cache=True, cache_dir="csv_cache_demo")
    
    print("\n1. 查看当前缓存信息")
    system.print_cache_info()
    
    # 下载另一个股票的数据
    print(f"\n2. 下载另一个股票数据")
    symbol2 = "MSFT.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 2, 28)
    
    results = system.run_backtest(
        symbol=symbol2,
        start_date=start_date,
        end_date=end_date,
        initial_cash=50000
    )
    
    if results:
        print(f"✓ {symbol2} 数据下载完成")
        
        print(f"\n3. 查看更新后的缓存信息")
        system.print_cache_info()
        
        # 列出所有CSV文件
        cache_dir = "csv_cache_demo"
        if os.path.exists(cache_dir):
            csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
            print(f"\n📁 缓存目录中的CSV文件:")
            for i, csv_file in enumerate(csv_files, 1):
                file_path = os.path.join(cache_dir, csv_file)
                file_size = os.path.getsize(file_path)
                print(f"   {i}. {csv_file} ({file_size} 字节)")
    
    return system

def demo_csv_file_inspection():
    """演示CSV文件检查功能"""
    print("\n" + "="*60)
    print("CSV文件检查功能演示")
    print("="*60)
    
    cache_dir = "csv_cache_demo"
    
    if not os.path.exists(cache_dir):
        print("❌ 缓存目录不存在，请先运行基本功能演示")
        return
    
    csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
    
    if not csv_files:
        print("❌ 未找到CSV缓存文件")
        return
    
    print(f"找到 {len(csv_files)} 个CSV缓存文件:")
    
    for i, csv_file in enumerate(csv_files, 1):
        file_path = os.path.join(cache_dir, csv_file)
        print(f"\n--- 文件 {i}: {csv_file} ---")
        
        try:
            # 读取CSV文件
            df = pd.read_csv(file_path)
            
            print(f"📊 数据统计:")
            print(f"   - 数据行数: {len(df)}")
            print(f"   - 数据列数: {len(df.columns)}")
            print(f"   - 列名: {list(df.columns)}")
            print(f"   - 日期范围: {df['datetime'].iloc[0]} 到 {df['datetime'].iloc[-1]}")
            print(f"   - 价格范围: ${df['close'].min():.2f} - ${df['close'].max():.2f}")
            print(f"   - 平均成交量: {df['volume'].mean():,.0f}")
            
            print(f"\n📈 最近3天数据:")
            recent_data = df.tail(3)[['datetime', 'close', 'volume']]
            print(recent_data.to_string(index=False))
            
        except Exception as e:
            print(f"❌ 读取文件失败: {e}")

def demo_csv_cache_advantages():
    """演示CSV缓存的优势"""
    print("\n" + "="*60)
    print("CSV缓存优势演示")
    print("="*60)
    
    print("📋 CSV格式缓存的优势:")
    print("1. ✅ 可读性强 - 可以用Excel、记事本等工具直接打开")
    print("2. ✅ 通用性好 - 几乎所有数据分析工具都支持CSV格式")
    print("3. ✅ 便于检查 - 可以直接查看数据内容，验证数据正确性")
    print("4. ✅ 易于编辑 - 可以手动修改数据（如果需要）")
    print("5. ✅ 跨平台 - 在Windows、Mac、Linux上都可以使用")
    print("6. ✅ 版本控制 - 可以用Git等工具跟踪数据变化")
    
    cache_dir = "csv_cache_demo"
    if os.path.exists(cache_dir):
        csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
        if csv_files:
            sample_file = os.path.join(cache_dir, csv_files[0])
            print(f"\n💡 使用建议:")
            print(f"   - 用Excel打开: 双击文件 {sample_file}")
            print(f"   - 用记事本查看: 右键 -> 打开方式 -> 记事本")
            print(f"   - 用Python分析: pd.read_csv('{sample_file}')")
            print(f"   - 备份重要数据: 复制整个 {cache_dir} 目录")

def cleanup_demo_cache():
    """清理演示缓存"""
    print("\n" + "="*60)
    print("清理演示缓存")
    print("="*60)
    
    try:
        system = BacktestSystem(enable_cache=True, cache_dir="csv_cache_demo")
        
        print("是否要清理演示缓存？(y/n): ", end="")
        choice = input().lower().strip()
        
        if choice == 'y':
            system.clear_cache()
            print("✅ 演示缓存已清理")
            
            # 删除缓存目录
            import shutil
            if os.path.exists("csv_cache_demo"):
                shutil.rmtree("csv_cache_demo")
                print("✅ 演示缓存目录已删除")
        else:
            print("📁 演示缓存保留，您可以继续查看CSV文件")
            
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

def main():
    """主演示函数"""
    print("LongBridge回测系统CSV缓存功能演示")
    print("="*60)
    print("本演示将展示:")
    print("1. CSV缓存基本功能")
    print("2. CSV缓存管理功能")
    print("3. CSV文件检查功能")
    print("4. CSV缓存优势说明")
    print("="*60)
    
    try:
        # 运行各种演示
        system1 = demo_csv_cache_basic()
        system2 = demo_csv_cache_management()
        demo_csv_file_inspection()
        demo_csv_cache_advantages()
        
        print("\n" + "="*60)
        print("🎉 所有演示完成！")
        print("📁 您现在可以查看 csv_cache_demo 目录中的CSV文件")
        print("💡 这些文件可以用Excel、记事本等工具直接打开")
        print("="*60)
        
        # 询问是否清理缓存
        cleanup_demo_cache()
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查LongBridge API配置是否正确")

if __name__ == "__main__":
    main()
