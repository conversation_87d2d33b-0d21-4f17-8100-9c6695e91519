"""
多时间周期缓存功能测试脚本
========================

测试修改后的系统是否能正确处理不同时间周期的数据缓存。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

import os
import sys
import pandas as pd
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from lB_BT_Plotly import BacktestSystem, LongBridgeData, DataCacheManager
    from longport.openapi import Period
    print("✓ 成功导入多时间周期缓存功能模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def test_cache_key_generation():
    """测试缓存键生成是否正确区分不同时间周期"""
    print("\n=== 测试缓存键生成 ===")
    
    try:
        cache_manager = DataCacheManager("test_multi_cache")
        
        symbol = "AAPL.US"
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 5)
        
        # 测试不同时间周期的缓存键
        periods = ["Day", "Min_1", "Min_5", "Min_15", "Min_30", "Min_60"]
        
        print("生成的缓存键:")
        for period in periods:
            cache_key = cache_manager._generate_cache_key(symbol, start_date, end_date, period)
            print(f"  {period:>6}: {cache_key}")
        
        # 验证缓存键是否唯一
        cache_keys = [cache_manager._generate_cache_key(symbol, start_date, end_date, p) for p in periods]
        unique_keys = set(cache_keys)
        
        if len(cache_keys) == len(unique_keys):
            print("✓ 所有缓存键都是唯一的")
            return True
        else:
            print("✗ 发现重复的缓存键")
            return False
            
    except Exception as e:
        print(f"✗ 缓存键生成测试失败: {e}")
        return False

def test_mock_data_caching():
    """使用模拟数据测试不同时间周期的缓存"""
    print("\n=== 测试模拟数据缓存 ===")
    
    try:
        cache_manager = DataCacheManager("test_multi_cache")
        
        symbol = "TEST.US"
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 2)
        
        # 创建不同时间周期的模拟数据
        test_data = {
            "Day": pd.DataFrame({
                'open': [100.0, 101.0],
                'high': [105.0, 106.0],
                'low': [95.0, 96.0],
                'close': [102.0, 103.0],
                'volume': [1000000, 1100000]
            }, index=pd.date_range('2023-01-01', '2023-01-02', freq='D')),
            
            "Min_5": pd.DataFrame({
                'open': [100.0 + i*0.1 for i in range(10)],
                'high': [105.0 + i*0.1 for i in range(10)],
                'low': [95.0 + i*0.1 for i in range(10)],
                'close': [102.0 + i*0.1 for i in range(10)],
                'volume': [100000 + i*1000 for i in range(10)]
            }, index=pd.date_range('2023-01-01 09:30', periods=10, freq='5min')),
            
            "Min_1": pd.DataFrame({
                'open': [100.0 + i*0.01 for i in range(50)],
                'high': [105.0 + i*0.01 for i in range(50)],
                'low': [95.0 + i*0.01 for i in range(50)],
                'close': [102.0 + i*0.01 for i in range(50)],
                'volume': [10000 + i*100 for i in range(50)]
            }, index=pd.date_range('2023-01-01 09:30', periods=50, freq='1min'))
        }
        
        # 保存不同时间周期的数据
        saved_count = 0
        for period, data in test_data.items():
            success = cache_manager.save_data(symbol, start_date, end_date, data, period)
            if success:
                saved_count += 1
                print(f"✓ {period} 数据保存成功 ({len(data)} 条记录)")
            else:
                print(f"✗ {period} 数据保存失败")
        
        # 验证所有数据都保存成功
        if saved_count == len(test_data):
            print(f"✓ 所有 {saved_count} 个时间周期的数据都保存成功")
        else:
            print(f"✗ 只有 {saved_count}/{len(test_data)} 个时间周期的数据保存成功")
            return False
        
        # 测试读取数据
        loaded_count = 0
        for period, original_data in test_data.items():
            loaded_data = cache_manager.load_data(symbol, start_date, end_date, period)
            if loaded_data is not None:
                loaded_count += 1
                print(f"✓ {period} 数据读取成功 ({len(loaded_data)} 条记录)")
                
                # 验证数据一致性
                if len(loaded_data) == len(original_data):
                    print(f"  ✓ {period} 数据长度一致")
                else:
                    print(f"  ✗ {period} 数据长度不一致: {len(loaded_data)} vs {len(original_data)}")
            else:
                print(f"✗ {period} 数据读取失败")
        
        # 验证所有数据都读取成功
        if loaded_count == len(test_data):
            print(f"✓ 所有 {loaded_count} 个时间周期的数据都读取成功")
            return True
        else:
            print(f"✗ 只有 {loaded_count}/{len(test_data)} 个时间周期的数据读取成功")
            return False
            
    except Exception as e:
        print(f"✗ 模拟数据缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cache_file_naming():
    """测试缓存文件命名是否正确"""
    print("\n=== 测试缓存文件命名 ===")
    
    try:
        cache_dir = "test_multi_cache"
        
        if not os.path.exists(cache_dir):
            print("✗ 缓存目录不存在")
            return False
        
        # 查找CSV文件
        csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
        
        if not csv_files:
            print("✗ 未找到CSV缓存文件")
            return False
        
        print(f"找到 {len(csv_files)} 个CSV缓存文件:")
        
        expected_patterns = ["Day", "Min_5", "Min_1"]
        found_patterns = []
        
        for csv_file in csv_files:
            print(f"  {csv_file}")
            
            # 检查文件名是否包含时间周期信息
            for pattern in expected_patterns:
                if pattern in csv_file:
                    found_patterns.append(pattern)
                    break
        
        # 验证是否找到了所有预期的时间周期
        unique_found = set(found_patterns)
        if len(unique_found) == len(expected_patterns):
            print("✓ 所有时间周期的文件都正确命名")
            return True
        else:
            missing = set(expected_patterns) - unique_found
            print(f"✗ 缺少时间周期文件: {missing}")
            return False
            
    except Exception as e:
        print(f"✗ 缓存文件命名测试失败: {e}")
        return False

def test_cache_metadata():
    """测试缓存元数据是否正确记录时间周期"""
    print("\n=== 测试缓存元数据 ===")
    
    try:
        cache_manager = DataCacheManager("test_multi_cache")
        
        print("缓存元数据:")
        for cache_key, metadata in cache_manager.metadata.items():
            period = metadata.get('period', 'Unknown')
            data_points = metadata.get('data_points', 0)
            print(f"  {cache_key}: {period} ({data_points} 条记录)")
        
        # 验证是否有不同时间周期的元数据
        periods_in_metadata = set()
        for metadata in cache_manager.metadata.values():
            period = metadata.get('period')
            if period:
                periods_in_metadata.add(period)
        
        expected_periods = {"Day", "Min_5", "Min_1"}
        if periods_in_metadata >= expected_periods:
            print("✓ 元数据正确记录了所有时间周期")
            return True
        else:
            missing = expected_periods - periods_in_metadata
            print(f"✗ 元数据缺少时间周期: {missing}")
            return False
            
    except Exception as e:
        print(f"✗ 缓存元数据测试失败: {e}")
        return False

def cleanup_test_cache():
    """清理测试缓存"""
    print("\n=== 清理测试缓存 ===")
    
    try:
        cache_manager = DataCacheManager("test_multi_cache")
        cache_manager.clear_cache()
        print("✓ 测试缓存清理完成")
        
        # 删除测试缓存目录
        import shutil
        if os.path.exists("test_multi_cache"):
            shutil.rmtree("test_multi_cache")
            print("✓ 测试缓存目录删除完成")
            
    except Exception as e:
        print(f"✗ 清理测试缓存失败: {e}")

def main():
    """主测试函数"""
    print("LongBridge回测系统多时间周期缓存功能测试")
    print("="*60)
    
    tests = [
        ("缓存键生成测试", test_cache_key_generation),
        ("模拟数据缓存测试", test_mock_data_caching),
        ("缓存文件命名测试", test_cache_file_naming),
        ("缓存元数据测试", test_cache_metadata),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 清理测试数据
    cleanup_test_cache()
    
    # 显示测试结果
    print(f"\n{'='*60}")
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("🎉 所有测试通过！多时间周期缓存功能正常工作。")
        print("📁 系统现在可以正确区分和管理不同时间周期的缓存数据。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
