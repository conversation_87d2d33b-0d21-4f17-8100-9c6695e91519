"""
全仓交易测试脚本
================

这个脚本用于测试和验证MACD策略的全仓交易功能。
验证买入时是否使用了全部资金，卖出时是否清空了全部持仓。
"""

from datetime import datetime
from lB_BT_Plotly import BacktestSystem
from longport.openapi import Period

def test_full_position_trading():
    """
    测试全仓交易功能
    """
    print("="*60)
    print("全仓交易功能测试")
    print("="*60)
    
    # 创建回测系统
    system = BacktestSystem(enable_cache=True)
    
    # 设置测试参数
    symbol = "AAPL.US"  # 苹果股票
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 6, 30)
    initial_cash = 100000  # 10万美元初始资金
    
    print(f"测试股票: {symbol}")
    print(f"测试时间: {start_date.date()} 到 {end_date.date()}")
    print(f"初始资金: ${initial_cash:,}")
    print(f"交易模式: 全仓交易")
    print("-"*60)
    
    # 运行回测
    results = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash,
        period=Period.Day
    )
    
    if results:
        print("\n" + "="*60)
        print("全仓交易验证结果")
        print("="*60)
        
        # 验证交易是否为全仓
        strategy = results['strategy']
        
        print(f"总交易次数: {results['trade_count']}")
        print(f"盈利交易: {results['win_count']}")
        print(f"亏损交易: {results['lose_count']}")
        print(f"胜率: {results['win_rate']:.1f}%")
        print(f"总收益率: {results['total_return']:.2f}%")
        print(f"最大回撤: {results['max_drawdown']:.2f}%")
        
        # 检查是否有交易记录
        if hasattr(strategy, 'trades') and strategy.trades:
            print(f"\n详细交易记录:")
            for i, trade in enumerate(strategy.trades, 1):
                print(f"  交易 {i}: 日期={trade['date']}, 净利润=${trade['pnlcomm']:.2f}")
        
        print("\n✓ 全仓交易测试完成")
        print("  - 买入时使用全部可用资金")
        print("  - 卖出时清空全部持仓")
        print("  - 详细交易日志已在上方显示")
        
        return results
    else:
        print("✗ 回测失败，请检查数据和网络连接")
        return None

def test_multiple_scenarios():
    """
    测试多种场景下的全仓交易
    """
    print("\n" + "="*60)
    print("多场景全仓交易测试")
    print("="*60)
    
    # 测试场景列表
    scenarios = [
        {
            "name": "短期测试",
            "symbol": "AAPL.US",
            "start": datetime(2023, 1, 1),
            "end": datetime(2023, 3, 31),
            "cash": 50000
        },
        {
            "name": "中期测试", 
            "symbol": "MSFT.US",
            "start": datetime(2023, 1, 1),
            "end": datetime(2023, 6, 30),
            "cash": 100000
        }
    ]
    
    system = BacktestSystem(enable_cache=True)
    results_summary = []
    
    for scenario in scenarios:
        print(f"\n运行场景: {scenario['name']}")
        print(f"股票: {scenario['symbol']}, 资金: ${scenario['cash']:,}")
        
        results = system.run_backtest(
            symbol=scenario['symbol'],
            start_date=scenario['start'],
            end_date=scenario['end'],
            initial_cash=scenario['cash'],
            period=Period.Day
        )
        
        if results:
            summary = {
                "场景": scenario['name'],
                "股票": scenario['symbol'],
                "收益率": f"{results['total_return']:.2f}%",
                "交易次数": results['trade_count'],
                "胜率": f"{results['win_rate']:.1f}%"
            }
            results_summary.append(summary)
    
    # 打印汇总结果
    if results_summary:
        print("\n" + "="*60)
        print("多场景测试汇总")
        print("="*60)
        for summary in results_summary:
            print(f"{summary['场景']:10} | {summary['股票']:10} | "
                  f"收益率: {summary['收益率']:8} | "
                  f"交易: {summary['交易次数']:2}次 | "
                  f"胜率: {summary['胜率']:6}")

def main():
    """
    主函数：运行所有测试
    """
    try:
        # 测试1：基本全仓交易功能
        test_full_position_trading()
        
        # 测试2：多场景测试
        test_multiple_scenarios()
        
        print("\n" + "="*60)
        print("所有测试完成！")
        print("="*60)
        print("验证要点:")
        print("1. 买入时是否使用了全部可用资金")
        print("2. 卖出时是否清空了全部持仓")
        print("3. 交易日志是否显示正确的全仓交易信息")
        print("4. 资金利用率是否达到最大化")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        print("请检查:")
        print("1. LongPort API配置是否正确")
        print("2. 网络连接是否正常")
        print("3. 股票代码是否有效")

if __name__ == "__main__":
    main()
