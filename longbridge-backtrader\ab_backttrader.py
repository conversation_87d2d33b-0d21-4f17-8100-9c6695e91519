import backtrader as bt
from xtquant import xdata, xorder, xposition, xaccount, xtrader

class YanBackTrader():
    def __init__(self, code_list, period):
        self.code_list = code_list
        self.period = period
        self.data = None

    def download_data(self):
        for code in self.code_list:
            xdata.download_history_data(code, period=self.period, incrementally=True)

    def get_history_data(self):
        return xdata.get_market_data_ex([], self.code_list, period=self.period, count=-1)
    
    def unit_test(self):
        # 这里可以添加单元测试代码
        pass
    
if __name__ == "__main__":
    ybt = YanBackTrader(["513050.SZ"], "1min")
    ybt.unit_test()