#!/usr/bin/env python3
"""
自定义绘图器示例
================

演示如何继承BacktestPlotter类创建自定义绘图器，
以及如何使用升级后的绘图系统。

功能特点：
1. 展示绘图器的继承和扩展
2. 自定义颜色主题和样式
3. 添加额外的技术指标
4. 演示多种绘图配置
"""

from lB_BT_Plotly import BacktestSystem, BacktestPlotter
from datetime import datetime
import plotly.graph_objects as go


class CustomBacktestPlotter(BacktestPlotter):
    """
    自定义回测绘图器
    ===============
    
    继承自BacktestPlotter，添加自定义功能和样式。
    """
    
    def __init__(self, figsize=(1400, 900), theme='plotly_dark'):
        """
        初始化自定义绘图器
        
        Args:
            figsize (tuple): 图表尺寸
            theme (str): 图表主题
        """
        super().__init__(figsize, theme)
        
        # 自定义颜色方案
        self.colors = {
            'buy_signal': '#00FF00',      # 亮绿色
            'sell_signal': '#FF0000',     # 亮红色
            'macd_line': '#00BFFF',       # 深天蓝色
            'signal_line': '#FFD700',     # 金色
            'histogram_positive': '#32CD32',  # 酸橙绿
            'histogram_negative': '#DC143C'   # 深红色
        }
    
    def _add_volume_chart(self, fig, plot_df):
        """
        添加成交量图表（新功能）
        
        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
        """
        x_values = list(range(len(plot_df)))
        
        # 根据价格涨跌设置成交量颜色
        colors = []
        for i in range(len(plot_df)):
            if i == 0:
                colors.append('gray')
            elif plot_df['close'].iloc[i] >= plot_df['close'].iloc[i-1]:
                colors.append('green')
            else:
                colors.append('red')
        
        fig.add_trace(
            go.Bar(
                x=x_values,
                y=plot_df['volume'],
                marker_color=colors,
                name='成交量',
                showlegend=True,
                opacity=0.6
            ),
            row=4, col=1  # 添加到第4行
        )
    
    def _create_subplot_layout(self, symbol):
        """
        重写子图布局，添加成交量图
        
        Args:
            symbol (str): 股票代码
            
        Returns:
            plotly.graph_objects.Figure: 子图布局
        """
        from plotly.subplots import make_subplots
        
        fig = make_subplots(
            rows=4, cols=1,  # 增加到4行
            shared_xaxes=True,
            vertical_spacing=0.02,
            row_heights=[0.5, 0.15, 0.15, 0.2],  # 调整高度比例
            subplot_titles=(
                f'{symbol} 价格走势与交易信号',
                'MACD指标', 
                'MACD柱状图',
                '成交量'
            )
        )
        return fig
    
    def plot_macd_strategy_results(self, results_dict):
        """
        重写绘图方法，添加成交量图
        
        Args:
            results_dict (dict): 回测结果字典
            
        Returns:
            plotly.graph_objects.Figure: 绘制完成的图表对象
        """
        symbol = results_dict['symbol']
        df = results_dict['data'].copy()
        
        # 准备绘图数据
        plot_df = self._prepare_data_for_plotting(df)
        
        # 计算MACD指标
        plot_df = self._calculate_macd_indicators(plot_df)
        
        # 识别交易信号
        buy_signals, sell_signals = self._identify_trading_signals(plot_df)
        
        # 创建子图布局（包含成交量）
        fig = self._create_subplot_layout(symbol)
        
        # 添加价格图表
        self._add_price_chart(fig, plot_df, buy_signals, sell_signals)
        
        # 添加MACD指标图
        self._add_macd_chart(fig, plot_df)
        
        # 添加MACD直方图
        self._add_macd_histogram(fig, plot_df)
        
        # 添加成交量图（新功能）
        self._add_volume_chart(fig, plot_df)
        
        # 更新布局和样式
        self._update_layout_with_volume(fig, plot_df, results_dict)
        
        return fig
    
    def _update_layout_with_volume(self, fig, plot_df, results_dict):
        """
        更新包含成交量的布局
        
        Args:
            fig: Plotly图表对象
            plot_df: 绘图数据
            results_dict: 回测结果字典
        """
        # 调用父类的布局更新方法
        self._update_layout(fig, plot_df, results_dict)
        
        # 为成交量图添加y轴标签
        fig.update_yaxes(title_text="成交量", row=4, col=1)
        
        # 调整图表高度以适应额外的子图
        fig.update_layout(height=self.figsize[1])


def demonstrate_custom_plotter():
    """
    演示自定义绘图器的使用
    """
    print("=" * 60)
    print("自定义绘图器演示")
    print("=" * 60)
    
    # 创建自定义绘图器
    custom_plotter = CustomBacktestPlotter(
        figsize=(1400, 1000),  # 更大的图表尺寸
        theme='plotly_dark'    # 深色主题
    )
    
    # 创建回测系统，使用自定义绘图器
    backtest_system = BacktestSystem(plotter=custom_plotter)
    
    # 设置回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2024, 1, 1)
    initial_cash = 100000
    
    print(f"使用自定义绘图器进行回测...")
    print(f"特点：深色主题 + 成交量图 + 自定义颜色")
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        # 使用自定义绘图器绘制结果
        fig = backtest_system.plot_results(symbol)
        if fig:
            fig.show()
            print(f"\n自定义图表已显示！")
            print("特色功能：")
            print("- 深色主题更适合长时间观看")
            print("- 成交量图帮助分析市场活跃度")
            print("- 自定义颜色方案更加醒目")
        else:
            print("图表生成失败")
    else:
        print("回测失败")


def demonstrate_multiple_plotters():
    """
    演示使用多种绘图器
    """
    print("\n" + "=" * 60)
    print("多绘图器对比演示")
    print("=" * 60)
    
    # 创建回测系统（使用默认绘图器）
    backtest_system = BacktestSystem()
    
    # 设置回测参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 6, 1)
    end_date = datetime(2023, 12, 31)
    initial_cash = 50000
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        print("\n1. 使用默认绘图器...")
        fig1 = backtest_system.plot_results(symbol)
        if fig1:
            fig1.show()
        
        print("\n2. 使用自定义绘图器...")
        custom_plotter = CustomBacktestPlotter()
        fig2 = backtest_system.plot_results(symbol, custom_plotter)
        if fig2:
            fig2.show()
        
        print("\n两种绘图器对比完成！")
        print("可以看到不同的样式和功能差异。")


if __name__ == "__main__":
    print("BacktestPlotter 自定义绘图器演示")
    print("=" * 60)
    print("本示例展示如何：")
    print("1. 继承BacktestPlotter创建自定义绘图器")
    print("2. 添加新的图表功能（如成交量）")
    print("3. 自定义颜色和主题")
    print("4. 在回测系统中使用不同的绘图器")
    print("=" * 60)
    
    # 运行演示
    demonstrate_custom_plotter()
    
    # 可选：运行多绘图器对比
    # demonstrate_multiple_plotters()
