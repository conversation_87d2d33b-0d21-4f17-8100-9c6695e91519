"""
多时间周期回测演示脚本
====================

演示如何使用不同时间周期进行回测，以及缓存系统如何管理不同周期的数据。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

from lB_BT_Plotly import BacktestSystem
from longport.openapi import Period
from datetime import datetime
import time

def demo_different_timeframes():
    """演示不同时间周期的回测"""
    print("\n" + "="*60)
    print("多时间周期回测演示")
    print("="*60)
    
    # 创建回测系统
    system = BacktestSystem(enable_cache=True, cache_dir="multi_timeframe_cache")
    
    # 测试参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 12, 1)
    end_date = datetime(2023, 12, 5)  # 较短的时间范围，适合分钟级数据
    initial_cash = 100000
    
    # 定义要测试的时间周期
    timeframes = [
        (Period.Day, "日线"),
        (Period.Min_60, "60分钟线"),
        (Period.Min_30, "30分钟线"),
        (Period.Min_15, "15分钟线"),
        (Period.Min_5, "5分钟线"),
        # Period.Min_1 数据量会很大，暂时不演示
    ]
    
    results = {}
    
    print(f"\n将使用以下时间周期进行回测:")
    for period, name in timeframes:
        print(f"  - {name} ({period})")
    
    print(f"\n开始多时间周期回测...")
    
    for i, (period, name) in enumerate(timeframes, 1):
        print(f"\n{'='*20} {i}/{len(timeframes)}: {name} {'='*20}")
        
        try:
            start_time = time.time()
            
            # 运行回测
            result = system.run_backtest(
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                initial_cash=initial_cash,
                period=period,
                force_download=False  # 使用缓存
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if result:
                results[name] = {
                    'result': result,
                    'duration': duration,
                    'data_points': len(result['data'])
                }
                print(f"✓ {name} 回测完成，耗时 {duration:.2f} 秒")
                print(f"  数据点数: {len(result['data'])} 条")
                print(f"  总收益率: {result['total_return']:.2f}%")
            else:
                print(f"✗ {name} 回测失败")
                
        except Exception as e:
            print(f"✗ {name} 回测异常: {e}")
    
    return results, system

def demo_cache_efficiency():
    """演示缓存效率"""
    print("\n" + "="*60)
    print("缓存效率演示")
    print("="*60)
    
    system = BacktestSystem(enable_cache=True, cache_dir="multi_timeframe_cache")
    
    symbol = "MSFT.US"
    start_date = datetime(2023, 12, 1)
    end_date = datetime(2023, 12, 3)
    
    # 测试5分钟线的缓存效率
    period = Period.Min_5
    
    print(f"\n测试 {symbol} 5分钟线数据的缓存效率...")
    
    # 首次下载
    print("\n1. 首次下载（会缓存数据）:")
    start_time = time.time()
    result1 = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        period=period,
        force_download=False
    )
    time1 = time.time() - start_time
    
    if result1:
        print(f"   首次下载耗时: {time1:.2f} 秒")
        print(f"   数据点数: {len(result1['data'])} 条")
    
    # 使用缓存
    print("\n2. 使用缓存数据:")
    start_time = time.time()
    result2 = system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        period=period,
        force_download=False
    )
    time2 = time.time() - start_time
    
    if result2:
        print(f"   缓存读取耗时: {time2:.2f} 秒")
        print(f"   数据点数: {len(result2['data'])} 条")
        
        if time1 > 0 and time2 > 0:
            speedup = time1 / time2
            print(f"   速度提升: {speedup:.1f}x 倍")
    
    return system

def demo_cache_management():
    """演示缓存管理功能"""
    print("\n" + "="*60)
    print("多时间周期缓存管理演示")
    print("="*60)
    
    system = BacktestSystem(enable_cache=True, cache_dir="multi_timeframe_cache")
    
    print("\n1. 查看当前缓存状态:")
    system.print_cache_info()
    
    # 查看缓存目录中的文件
    import os
    cache_dir = "multi_timeframe_cache"
    if os.path.exists(cache_dir):
        csv_files = [f for f in os.listdir(cache_dir) if f.endswith('.csv')]
        
        print(f"\n2. 缓存文件列表 ({len(csv_files)} 个文件):")
        
        # 按时间周期分组显示
        timeframe_files = {}
        for csv_file in csv_files:
            # 从文件名中提取时间周期
            parts = csv_file.split('_')
            if len(parts) >= 2:
                timeframe = parts[1]  # 例如从 "AAPL.US_Day_20231201_20231205.csv" 提取 "Day"
                if timeframe not in timeframe_files:
                    timeframe_files[timeframe] = []
                timeframe_files[timeframe].append(csv_file)
        
        for timeframe, files in timeframe_files.items():
            print(f"\n   {timeframe} 级别:")
            for file in files:
                file_path = os.path.join(cache_dir, file)
                file_size = os.path.getsize(file_path)
                print(f"     - {file} ({file_size} 字节)")
    
    print(f"\n3. 缓存管理建议:")
    print(f"   - 不同时间周期的数据分别缓存，互不干扰")
    print(f"   - 可以选择性清理特定时间周期的缓存")
    print(f"   - 分钟级数据文件较大，建议定期清理")
    print(f"   - 日线数据文件较小，可以长期保留")

def demo_practical_usage():
    """演示实际使用场景"""
    print("\n" + "="*60)
    print("实际使用场景演示")
    print("="*60)
    
    print("\n📊 多时间周期分析的实际应用:")
    print("1. 日线级别 - 适合长期趋势分析和策略验证")
    print("2. 60分钟级别 - 适合日内交易策略")
    print("3. 15分钟级别 - 适合短线交易策略")
    print("4. 5分钟级别 - 适合高频交易策略")
    print("5. 1分钟级别 - 适合超高频交易策略")
    
    print("\n💡 使用建议:")
    print("1. 策略开发:")
    print("   - 先用日线数据验证策略逻辑")
    print("   - 再用分钟级数据优化参数")
    print("   - 最后用高频数据测试实际表现")
    
    print("\n2. 数据管理:")
    print("   - 日线数据：长期保留，文件小")
    print("   - 小时级数据：中期保留，文件适中")
    print("   - 分钟级数据：短期保留，文件大")
    
    print("\n3. 性能优化:")
    print("   - 使用缓存避免重复下载")
    print("   - 分时间周期管理缓存")
    print("   - 定期清理不需要的高频数据")

def cleanup_demo_cache():
    """清理演示缓存"""
    print("\n" + "="*60)
    print("清理演示缓存")
    print("="*60)
    
    try:
        system = BacktestSystem(enable_cache=True, cache_dir="multi_timeframe_cache")
        
        print("是否要清理演示缓存？(y/n): ", end="")
        choice = input().lower().strip()
        
        if choice == 'y':
            system.clear_cache()
            print("✅ 演示缓存已清理")
            
            # 删除缓存目录
            import shutil
            import os
            if os.path.exists("multi_timeframe_cache"):
                shutil.rmtree("multi_timeframe_cache")
                print("✅ 演示缓存目录已删除")
        else:
            print("📁 演示缓存保留，您可以继续查看CSV文件")
            
    except Exception as e:
        print(f"❌ 清理缓存失败: {e}")

def main():
    """主演示函数"""
    print("LongBridge回测系统多时间周期功能演示")
    print("="*60)
    print("本演示将展示:")
    print("1. 不同时间周期的回测")
    print("2. 缓存效率对比")
    print("3. 多时间周期缓存管理")
    print("4. 实际使用场景说明")
    print("="*60)
    
    try:
        # 运行各种演示
        results, system1 = demo_different_timeframes()
        system2 = demo_cache_efficiency()
        demo_cache_management()
        demo_practical_usage()
        
        print("\n" + "="*60)
        print("🎉 所有演示完成！")
        print("📁 您现在可以查看 multi_timeframe_cache 目录中的CSV文件")
        print("💡 不同时间周期的数据已分别缓存，可以用Excel等工具查看")
        print("="*60)
        
        # 询问是否清理缓存
        cleanup_demo_cache()
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        print("请检查LongBridge API配置是否正确")

if __name__ == "__main__":
    main()
