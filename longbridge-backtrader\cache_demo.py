"""
缓存功能演示脚本
===============

这个脚本演示了如何使用LongBridge回测系统的缓存功能。
展示了数据缓存、读取、管理等各种功能的使用方法。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

from lB_BT_Plotly import BacktestSystem, LongBridgeData
from datetime import datetime
import time

def demo_basic_cache():
    """演示基本缓存功能"""
    print("\n" + "="*60)
    print("基本缓存功能演示")
    print("="*60)
    
    # 创建启用缓存的回测系统
    system = BacktestSystem(enable_cache=True, cache_dir="demo_cache")
    
    # 测试参数
    symbol = "AAPL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 6, 30)
    
    print("\n1. 首次下载数据（会自动缓存）")
    start_time = time.time()
    results1 = system.run_backtest(symbol, start_date, end_date, initial_cash=100000)
    time1 = time.time() - start_time
    print(f"首次下载耗时: {time1:.2f} 秒")
    
    print("\n2. 再次运行相同回测（使用缓存）")
    start_time = time.time()
    results2 = system.run_backtest(symbol, start_date, end_date, initial_cash=100000)
    time2 = time.time() - start_time
    print(f"缓存读取耗时: {time2:.2f} 秒")
    
    print(f"\n速度提升: {time1/time2:.1f}x 倍")
    
    return system

def demo_cache_management():
    """演示缓存管理功能"""
    print("\n" + "="*60)
    print("缓存管理功能演示")
    print("="*60)
    
    # 创建回测系统
    system = BacktestSystem(enable_cache=True, cache_dir="demo_cache")
    
    # 下载多个股票的数据
    symbols = ["AAPL.US", "MSFT.US"]
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 31)
    
    print("\n1. 下载多个股票数据")
    for symbol in symbols:
        print(f"\n下载 {symbol} 数据...")
        system.run_backtest(symbol, start_date, end_date, initial_cash=50000)
    
    print("\n2. 查看缓存信息")
    system.print_cache_info()
    
    print("\n3. 清理特定股票缓存")
    system.clear_cache("AAPL.US")
    system.print_cache_info()
    
    print("\n4. 清理全部缓存")
    system.clear_cache()
    system.print_cache_info()

def demo_force_download():
    """演示强制重新下载功能"""
    print("\n" + "="*60)
    print("强制重新下载功能演示")
    print("="*60)
    
    system = BacktestSystem(enable_cache=True, cache_dir="demo_cache")
    
    symbol = "TSLA.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 31)
    
    print("\n1. 首次下载数据")
    system.run_backtest(symbol, start_date, end_date, initial_cash=100000)
    
    print("\n2. 使用缓存数据")
    system.run_backtest(symbol, start_date, end_date, initial_cash=100000, force_download=False)
    
    print("\n3. 强制重新下载")
    system.run_backtest(symbol, start_date, end_date, initial_cash=100000, force_download=True)

def demo_data_downloader_only():
    """演示单独使用数据下载器的缓存功能"""
    print("\n" + "="*60)
    print("数据下载器缓存功能演示")
    print("="*60)
    
    # 创建数据下载器
    downloader = LongBridgeData(enable_cache=True, cache_dir="demo_cache")
    
    symbol = "GOOGL.US"
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 3, 31)
    
    print("\n1. 首次下载数据")
    data1 = downloader.download_data(symbol, start_date, end_date)
    print(f"数据形状: {data1.shape if data1 is not None else 'None'}")
    
    print("\n2. 从缓存读取数据")
    data2 = downloader.download_data(symbol, start_date, end_date)
    print(f"数据形状: {data2.shape if data2 is not None else 'None'}")
    
    print("\n3. 查看缓存信息")
    downloader.print_cache_info()
    
    print("\n4. 清理缓存")
    downloader.clear_cache(symbol)

def main():
    """主演示函数"""
    print("LongBridge回测系统缓存功能演示")
    print("="*60)
    print("本演示将展示以下功能:")
    print("1. 基本缓存功能")
    print("2. 缓存管理功能") 
    print("3. 强制重新下载功能")
    print("4. 数据下载器缓存功能")
    print("="*60)
    
    try:
        # 运行各种演示
        demo_basic_cache()
        demo_cache_management()
        demo_force_download()
        demo_data_downloader_only()
        
        print("\n" + "="*60)
        print("所有演示完成！")
        print("="*60)
        
    except Exception as e:
        print(f"\n演示过程中出现错误: {e}")
        print("请检查LongBridge API配置是否正确")

if __name__ == "__main__":
    main()
