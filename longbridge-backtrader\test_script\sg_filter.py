import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from scipy import signal

# 模拟股票价格数据
dates = pd.date_range('2024-01-01', periods=150, freq='D')
prices = 100 + np.cumsum(np.random.randn(150) * 0.8)

# 计算MACD
def calculate_macd(prices, fast=12, slow=26, signal_period=9):
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd_line = ema_fast - ema_slow
    signal_line = macd_line.ewm(span=signal_period).mean()
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

macd, signal_line, histogram = calculate_macd(pd.Series(prices))

# 因果Savitzky-Golay滤波器（只使用左侧数据）
def causal_savgol_filter(data, window=11, polyorder=2):
    """
    因果Savitzky-Golay滤波器，只使用历史数据
    """
    filtered = np.zeros_like(data)
    
    for i in range(len(data)):
        # 确定滤波窗口的起始位置
        start_idx = max(0, i - window + 1)
        end_idx = i + 1
        
        # 如果数据点不够，就用现有的数据
        window_data = data[start_idx:end_idx]
        actual_window = len(window_data)
        
        if actual_window >= 3:  # 至少需要3个点才能做2阶多项式拟合
            # 调整多项式阶数
            actual_polyorder = min(polyorder, actual_window - 1)
            
            # 对窗口内数据进行Savitzky-Golay滤波
            # 只取最后一个点（当前点）的滤波结果
            window_filtered = signal.savgol_filter(window_data, 
                                                  actual_window if actual_window % 2 == 1 else actual_window - 1,
                                                  actual_polyorder)
            filtered[i] = window_filtered[-1]
        else:
            # 数据不够时直接使用原值
            filtered[i] = data[i]
    
    return pd.Series(filtered, index=data.index)

# 实时滚动Savitzky-Golay滤波器（更高效的实现）
def rolling_savgol_filter(data, window=11, polyorder=2):
    """
    更高效的实时Savitzky-Golay滤波器
    """
    def apply_savgol(x):
        if len(x) < 3:
            return x.iloc[-1]
        actual_window = len(x)
        actual_polyorder = min(polyorder, actual_window - 1)
        # 确保窗口大小为奇数
        if actual_window % 2 == 0:
            actual_window -= 1
            x = x.iloc[-actual_window:]
        
        filtered = signal.savgol_filter(x, actual_window, actual_polyorder)
        return filtered[-1]  # 只返回最后一个点
    
    # 使用expanding window，然后限制最大窗口大小
    result = []
    for i in range(len(data)):
        start_idx = max(0, i - window + 1)
        window_data = data.iloc[start_idx:i+1]
        filtered_value = apply_savgol(window_data)
        result.append(filtered_value)
    
    return pd.Series(result, index=data.index)

# 简化版本：使用pandas rolling + 自定义函数
def simple_causal_savgol(data, window=11, polyorder=2):
    """
    最简单的实现方式
    """
    def savgol_last_point(x):
        if len(x) < 3:
            return x.iloc[-1]
        
        # 确保窗口为奇数
        w = len(x) if len(x) % 2 == 1 else len(x) - 1
        p = min(polyorder, w - 1)
        
        if w >= 3:
            filtered = signal.savgol_filter(x.iloc[-w:], w, p)
            return filtered[-1]
        return x.iloc[-1]
    
    return data.rolling(window=window, min_periods=1).apply(savgol_last_point, raw=False)

# 应用不同的滤波方法
hist_original = histogram
hist_centered_sg = pd.Series(signal.savgol_filter(histogram, 11, 2), index=histogram.index)  # 传统中心对称
hist_causal_sg = causal_savgol_filter(histogram, window=11, polyorder=2)  # 因果滤波
hist_rolling_sg = rolling_savgol_filter(histogram, window=11, polyorder=2)  # 滚动滤波
hist_simple_sg = simple_causal_savgol(histogram, window=11, polyorder=2)  # 简化版本

# 创建对比图
fig = make_subplots(
    rows=2, cols=2,
    subplot_titles=['原始 vs 传统SG滤波', '原始 vs 因果SG滤波',
                   '各种因果SG滤波对比', '滤波延迟对比'],
    vertical_spacing=0.1
)

# 第一个子图：原始 vs 传统SG
fig.add_trace(go.Scatter(x=dates, y=hist_original, name='原始', 
                        line=dict(color='black', width=1)), row=1, col=1)
fig.add_trace(go.Scatter(x=dates, y=hist_centered_sg, name='传统SG', 
                        line=dict(color='blue')), row=1, col=1)

# 第二个子图：原始 vs 因果SG
fig.add_trace(go.Scatter(x=dates, y=hist_original, name='原始', 
                        line=dict(color='black', width=1), showlegend=False), row=1, col=2)
fig.add_trace(go.Scatter(x=dates, y=hist_causal_sg, name='因果SG', 
                        line=dict(color='red')), row=1, col=2)

# 第三个子图：各种因果实现对比
fig.add_trace(go.Scatter(x=dates, y=hist_causal_sg, name='因果SG v1', 
                        line=dict(color='red')), row=2, col=1)
fig.add_trace(go.Scatter(x=dates, y=hist_rolling_sg, name='滚动SG', 
                        line=dict(color='green')), row=2, col=1)
fig.add_trace(go.Scatter(x=dates, y=hist_simple_sg, name='简化SG', 
                        line=dict(color='purple')), row=2, col=1)

# 第四个子图：延迟对比（放大最后50个点）
last_50_dates = dates[-50:]
fig.add_trace(go.Scatter(x=last_50_dates, y=hist_original[-50:], name='原始', 
                        line=dict(color='black')), row=2, col=2)
fig.add_trace(go.Scatter(x=last_50_dates, y=hist_centered_sg[-50:], name='传统SG(有延迟)', 
                        line=dict(color='blue', dash='dash')), row=2, col=2)
fig.add_trace(go.Scatter(x=last_50_dates, y=hist_simple_sg[-50:], name='因果SG(无延迟)', 
                        line=dict(color='red')), row=2, col=2)

fig.update_layout(height=800, title_text="因果Savitzky-Golay滤波器对比")
fig.show()

# 实际使用建议
print("实时交易建议:")
print("1. 使用 simple_causal_savgol() - 最简单实用")
print("2. 窗口大小: 7-15 (奇数)")
print("3. 多项式阶数: 2-3")
print("4. 优点: 无未来信息泄露，适合实时决策")
print("5. 缺点: 比中心对称滤波噪声稍多")

# 参数调优示例
def optimize_causal_savgol(data, windows=[7,9,11,13,15], polyorders=[2,3]):
    """
    找到最佳的因果SG滤波参数
    """
    best_params = {}
    min_noise = float('inf')
    
    for w in windows:
        for p in polyorders:
            if p < w:  # 确保多项式阶数小于窗口
                filtered = simple_causal_savgol(data, window=w, polyorder=p)
                # 计算滤波后的噪声水平（可以用标准差或其他指标）
                noise_level = filtered.diff().std()
                
                if noise_level < min_noise:
                    min_noise = noise_level
                    best_params = {'window': w, 'polyorder': p, 'noise': noise_level}
    
    return best_params

# 找最佳参数
best = optimize_causal_savgol(histogram)
print(f"\n最佳参数: 窗口={best['window']}, 多项式阶数={best['polyorder']}")
print(f"噪声水平: {best['noise']:.4f}")