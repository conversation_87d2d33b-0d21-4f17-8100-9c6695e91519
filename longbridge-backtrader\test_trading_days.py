#!/usr/bin/env python3
"""
测试交易日连续性
================

验证新的绘图系统是否正确处理了非交易日的空白问题。
"""

from lB_BT_Plotly import BacktestSystem
from datetime import datetime
import pandas as pd


def test_trading_days_continuity():
    """
    测试交易日的连续性处理
    """
    print("=" * 50)
    print("测试交易日连续性处理")
    print("=" * 50)
    
    # 创建回测系统
    backtest_system = BacktestSystem()
    
    # 设置包含周末和节假日的时间段
    symbol = "AAPL.US"
    start_date = datetime(2023, 10, 1)  # 更安全的时间范围
    end_date = datetime(2023, 12, 31)   # 包含感恩节等节假日
    initial_cash = 100000
    
    print(f"测试时间段: {start_date.date()} 到 {end_date.date()}")
    print("该时间段包含多个非交易日：")
    print("- 周末")
    print("- 圣诞节假期")
    print("- 新年假期")
    
    # 运行回测
    results = backtest_system.run_backtest(
        symbol=symbol,
        start_date=start_date,
        end_date=end_date,
        initial_cash=initial_cash
    )
    
    if results:
        # 检查原始数据
        df = results['data']
        print(f"\n原始数据信息:")
        print(f"数据点数量: {len(df)}")
        print(f"数据日期范围: {df.index[0].date()} 到 {df.index[-1].date()}")
        
        # 检查是否有连续的日期（应该只有交易日）
        date_diffs = df.index.to_series().diff().dropna()
        weekend_gaps = date_diffs[date_diffs > pd.Timedelta(days=1)]
        
        print(f"\n非交易日间隙数量: {len(weekend_gaps)}")
        if len(weekend_gaps) > 0:
            print("主要间隙:")
            for date, gap in weekend_gaps.head().items():
                print(f"  {date.date()}: {gap.days}天间隙")
        
        # 生成图表
        print(f"\n生成图表...")
        fig = backtest_system.plot_results(symbol)
        
        if fig:
            # 检查图表的x轴数据
            price_trace = fig.data[0]  # 第一个trace应该是价格数据
            x_data = price_trace.x
            
            print(f"图表x轴数据类型: {type(x_data[0])}")
            print(f"图表数据点数量: {len(x_data)}")
            print(f"x轴数据范围: {min(x_data)} 到 {max(x_data)}")
            
            # 验证x轴是否连续
            if isinstance(x_data[0], int):
                is_continuous = all(x_data[i] == i for i in range(len(x_data)))
                print(f"x轴是否连续: {'是' if is_continuous else '否'}")
            
            fig.show()
            print(f"\n✅ 图表已显示！")
            print("请检查图表中是否还有非交易日的空白间隙。")
            print("新的绘图系统应该已经消除了这些空白。")
        else:
            print("❌ 图表生成失败")
    else:
        print("❌ 回测失败")


def compare_old_vs_new_approach():
    """
    对比新旧绘图方法的差异
    """
    print("\n" + "=" * 50)
    print("新旧绘图方法对比")
    print("=" * 50)
    
    print("旧方法问题:")
    print("- 直接使用日期作为x轴")
    print("- 非交易日显示为空白间隙")
    print("- 图表不够紧凑")
    
    print("\n新方法优势:")
    print("- 使用连续整数作为x轴")
    print("- 自定义日期标签显示")
    print("- 消除非交易日空白")
    print("- 图表更加紧凑和美观")
    
    print("\n技术实现:")
    print("1. 重置DataFrame索引为连续整数")
    print("2. 保存原始日期用于标签显示")
    print("3. 创建自定义x轴刻度和标签")
    print("4. 所有图表组件使用整数索引")


if __name__ == "__main__":
    print("交易日连续性测试")
    print("=" * 50)
    print("本测试验证新的绘图系统是否正确处理非交易日问题")
    print("=" * 50)
    
    # 运行测试
    test_trading_days_continuity()
    
    # 显示对比说明
    compare_old_vs_new_approach()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("如果图表中没有空白间隙，说明升级成功。")
    print("=" * 50)
