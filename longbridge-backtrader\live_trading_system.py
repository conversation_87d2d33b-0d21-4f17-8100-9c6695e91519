"""
实盘交易系统
============

基于Longbridge API的实盘MACD交易系统，包含数据获取、策略执行、风险控制等功能。
"""

import pandas as pd
import numpy as np
import time
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import threading
import signal
import sys

# Longbridge API导入
from longport.openapi import QuoteContext, TradeContext, Config, Period, AdjustType
from longport.openapi import OrderSide, OrderType, TimeInForce

# 本地模块导入
from live_macd_strategy import LiveMACDStrategy
from live_trading_config import get_trading_config, validate_config
from lB_BT_Plotly import LongBridgeData


class LiveTradingSystem:
    """
    实盘交易系统主类
    ================
    
    整合数据获取、策略执行、订单管理、风险控制等功能的完整实盘交易系统。
    """
    
    def __init__(self, config=None):
        """
        初始化实盘交易系统
        
        Args:
            config (dict, optional): 系统配置，如果不提供则使用默认配置
        """
        # 验证配置
        is_valid, errors = validate_config()
        if not is_valid:
            raise ValueError(f"配置验证失败: {errors}")
        
        self.config = config or get_trading_config()
        self.symbol = self.config['symbol']
        self.period = self.config['period']
        self.position_size = self.config['position_size']
        
        # 初始化Longbridge连接
        self._init_longbridge_connections()
        
        # 初始化策略
        self.strategy = LiveMACDStrategy(self.config)
        
        # 初始化数据下载器
        self.data_downloader = LongBridgeData(
            enable_cache=self.config['cache_config']['enable_cache'],
            cache_dir=self.config['cache_config']['cache_dir']
        )
        
        # 系统状态
        self.is_running = False
        self.last_data_update = None
        self.total_trades = 0
        self.total_pnl = 0.0
        self.paper_cash = self.config['trading_mode']['paper_initial_cash']
        self.paper_position = 0
        
        # 设置日志
        self.logger = self._setup_logger()
        
        # 设置信号处理器（优雅退出）
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        self.logger.info("实盘交易系统初始化完成")
        self.logger.info(f"交易标的: {self.symbol}")
        self.logger.info(f"交易模式: {self.config['trading_mode']['mode']}")
    
    def _init_longbridge_connections(self):
        """初始化Longbridge API连接"""
        try:
            # 从环境变量加载配置
            self.longbridge_config = Config.from_env()
            
            # 创建行情上下文（用于获取数据）
            self.quote_ctx = QuoteContext(self.longbridge_config)
            
            # 创建交易上下文（用于下单）
            if self.config['trading_mode']['mode'] == 'live':
                self.trade_ctx = TradeContext(self.longbridge_config)
            else:
                self.trade_ctx = None  # 模拟模式不需要真实交易连接
            
            self.logger.info("Longbridge API连接初始化成功")
            
        except Exception as e:
            self.logger.error(f"Longbridge API连接失败: {e}")
            raise
    
    def _setup_logger(self):
        """设置日志记录器"""
        logger = logging.getLogger('LiveTradingSystem')
        logger.setLevel(getattr(logging, self.config['logging_config']['log_level']))
        
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler(self.config['logging_config']['log_file'])
            file_handler.setLevel(logging.DEBUG)
            
            # 控制台处理器
            if self.config['logging_config']['enable_console_log']:
                console_handler = logging.StreamHandler()
                console_handler.setLevel(logging.INFO)
                
                # 格式化器
                formatter = logging.Formatter(
                    '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
                )
                file_handler.setFormatter(formatter)
                console_handler.setFormatter(formatter)
                
                logger.addHandler(file_handler)
                logger.addHandler(console_handler)
        
        return logger
    
    def _signal_handler(self, signum, frame):
        """信号处理器，用于优雅退出"""
        self.logger.info(f"接收到信号 {signum}，正在停止交易系统...")
        self.stop()
        sys.exit(0)
    
    def get_latest_data(self) -> Optional[pd.DataFrame]:
        """
        获取最新的市场数据
        
        Returns:
            pd.DataFrame: 最新的K线数据，如果获取失败则返回None
        """
        try:
            # 计算数据获取的时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config['data_config']['lookback_days'])
            
            # 使用数据下载器获取数据
            data = self.data_downloader.download_data(
                symbol=self.symbol,
                start_date=start_date,
                end_date=end_date,
                period=self.period,
                force_download=False  # 使用缓存以提高效率
            )
            
            if data is not None and not data.empty:
                self.last_data_update = datetime.now()
                self.logger.debug(f"成功获取数据，最新时间: {data.index[-1]}")
                return data
            else:
                self.logger.warning("获取到的数据为空")
                return None
                
        except Exception as e:
            self.logger.error(f"获取市场数据失败: {e}")
            return None
    
    def execute_trade(self, signal: int) -> bool:
        """
        执行交易
        
        Args:
            signal (int): 交易信号 (1: 买入, -1: 卖出)
            
        Returns:
            bool: 交易是否成功执行
        """
        try:
            # 检查是否可以交易
            can_trade, reason = self.strategy.can_trade(signal)
            if not can_trade:
                self.logger.info(f"无法交易: {reason}")
                return False
            
            # 获取当前价格
            latest_data = self.get_latest_data()
            if latest_data is None or latest_data.empty:
                self.logger.error("无法获取当前价格，交易取消")
                return False
            
            current_price = latest_data['close'].iloc[-1]
            trade_size = self.position_size
            
            if self.config['trading_mode']['mode'] == 'paper':
                # 模拟交易
                return self._execute_paper_trade(signal, trade_size, current_price)
            else:
                # 实盘交易
                return self._execute_live_trade(signal, trade_size, current_price)
                
        except Exception as e:
            self.logger.error(f"交易执行失败: {e}")
            return False
    
    def _execute_paper_trade(self, signal: int, trade_size: int, current_price: float) -> bool:
        """
        执行模拟交易
        
        Args:
            signal (int): 交易信号
            trade_size (int): 交易数量
            current_price (float): 当前价格
            
        Returns:
            bool: 交易是否成功
        """
        try:
            if signal == 1:  # 买入
                cost = trade_size * current_price
                if self.paper_cash >= cost:
                    self.paper_cash -= cost
                    self.paper_position += trade_size
                    self.logger.info(f"模拟买入: {trade_size}手 @ ${current_price:.2f}, 剩余现金: ${self.paper_cash:.2f}")
                else:
                    self.logger.warning("模拟账户资金不足，无法买入")
                    return False
                    
            elif signal == -1:  # 卖出
                if self.paper_position >= trade_size:
                    revenue = trade_size * current_price
                    self.paper_cash += revenue
                    self.paper_position -= trade_size
                    self.logger.info(f"模拟卖出: {trade_size}手 @ ${current_price:.2f}, 现金增加: ${revenue:.2f}")
                else:
                    self.logger.warning("模拟账户持仓不足，无法卖出")
                    return False
            
            # 更新策略状态
            self.strategy.update_position(signal, trade_size)
            self.total_trades += 1
            
            return True
            
        except Exception as e:
            self.logger.error(f"模拟交易执行失败: {e}")
            return False
    
    def _execute_live_trade(self, signal: int, trade_size: int, current_price: float) -> bool:
        """
        执行实盘交易
        
        Args:
            signal (int): 交易信号
            trade_size (int): 交易数量
            current_price (float): 当前价格
            
        Returns:
            bool: 交易是否成功
        """
        if self.trade_ctx is None:
            self.logger.error("实盘交易上下文未初始化")
            return False
        
        try:
            # 确定订单方向
            side = OrderSide.Buy if signal == 1 else OrderSide.Sell
            
            # 创建市价单
            order = self.trade_ctx.submit_order(
                symbol=self.symbol,
                order_type=OrderType.MO,  # 市价单
                side=side,
                submitted_quantity=trade_size,
                time_in_force=TimeInForce.Day,
                remark=f"MACD策略自动交易-{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            )
            
            if order:
                action = "买入" if signal == 1 else "卖出"
                self.logger.info(f"实盘{action}订单已提交: {trade_size}手, 订单ID: {order.order_id}")
                
                # 更新策略状态
                self.strategy.update_position(signal, trade_size)
                self.total_trades += 1
                
                return True
            else:
                self.logger.error("订单提交失败")
                return False
                
        except Exception as e:
            self.logger.error(f"实盘交易执行失败: {e}")
            return False
