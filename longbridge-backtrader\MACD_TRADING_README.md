# MACD直方图差分实盘交易系统

## 📋 系统概述

这是一个基于MACD直方图差分符号变化的实盘交易系统，从Longbridge获取1小时K线数据，通过分析MACD直方图的差分符号变化来生成交易信号，每次交易1手。

## 🎯 核心功能

### 1. 数据获取
- 从Longbridge API获取1小时K线数据
- 支持多种股票市场（美股、港股等）
- 自动处理数据复权和格式转换

### 2. 技术指标计算
- 计算MACD指标（快线、慢线、信号线、直方图）
- 对MACD直方图进行滤波处理
- 计算直方图的差分值

### 3. 交易信号生成
- **买入信号**: 直方图差分从负变正（上升趋势开始）
- **卖出信号**: 直方图差分从正变负（下降趋势开始）
- 支持最小阈值过滤，避免噪音交易

### 4. 风险控制
- 最大持仓限制
- 每日交易次数限制
- 交易时间控制
- 止损止盈设置

### 5. 监控和日志
- 实时交易日志记录
- 策略状态监控
- 错误处理和报警

## 📁 文件结构

```
├── macd_histogram_live_trading.py    # 基础版交易系统
├── enhanced_macd_trading.py          # 增强版交易系统（推荐）
├── macd_trading_config.py            # 配置文件
└── MACD_TRADING_README.md            # 使用说明（本文件）
```

## 🚀 快速开始

### 1. 环境准备

确保已安装必要的Python包：
```bash
pip install pandas numpy scipy longport
```

### 2. 配置Longbridge API

设置环境变量：
```bash
export LONGPORT_APP_KEY="your_app_key"
export LONGPORT_APP_SECRET="your_app_secret"
export LONGPORT_ACCESS_TOKEN="your_access_token"
```

或在Windows中：
```cmd
set LONGPORT_APP_KEY=your_app_key
set LONGPORT_APP_SECRET=your_app_secret
set LONGPORT_ACCESS_TOKEN=your_access_token
```

### 3. 配置交易参数

编辑 `macd_trading_config.py` 文件：

```python
# 交易标的配置
TRADING_CONFIG = {
    'symbol': 'AAPL.US',        # 修改为你要交易的股票
    'position_size': 1,         # 每次交易数量（手）
    'update_interval': 3600,    # 更新间隔（秒）
    'max_position': 10,         # 最大持仓限制
}

# MACD参数
MACD_CONFIG = {
    'fast_period': 12,          # 快线周期
    'slow_period': 26,          # 慢线周期
    'signal_period': 9,         # 信号线周期
}
```

### 4. 运行交易系统

**推荐使用增强版：**
```bash
python enhanced_macd_trading.py
```

**或使用基础版：**
```bash
python macd_histogram_live_trading.py
```

## ⚙️ 配置说明

### 交易配置 (TRADING_CONFIG)
- `symbol`: 交易标的，如 'AAPL.US', '00700.HK'
- `position_size`: 每次交易数量（手）
- `update_interval`: 数据更新间隔（秒）
- `max_position`: 最大持仓限制

### MACD参数 (MACD_CONFIG)
- `fast_period`: MACD快线周期（默认12）
- `slow_period`: MACD慢线周期（默认26）
- `signal_period`: MACD信号线周期（默认9）

### 滤波配置 (FILTER_CONFIG)
- `filter_window`: 滤波窗口长度
- `filter_type`: 滤波类型（'sma' 或 'ema'）
- `ema_alpha`: EMA滤波器平滑因子

### 信号配置 (SIGNAL_CONFIG)
- `min_threshold`: 最小差分阈值，避免噪音交易
- `signal_confirmation`: 信号确认周期数

### 风险控制 (RISK_CONFIG)
- `max_daily_trades`: 每日最大交易次数
- `stop_loss_pct`: 止损百分比
- `take_profit_pct`: 止盈百分比
- `trading_start_time`: 交易开始时间
- `trading_end_time`: 交易结束时间

## 📊 交易逻辑

### 信号生成逻辑
1. 获取1小时K线数据
2. 计算MACD指标的直方图
3. 对直方图进行滤波处理
4. 计算滤波后直方图的差分
5. 检测差分符号变化：
   - 差分从负变正 → 买入信号
   - 差分从正变负 → 卖出信号

### 风险控制逻辑
1. 检查交易时间是否在允许范围内
2. 检查每日交易次数是否超限
3. 检查持仓是否超过最大限制
4. 验证信号强度是否超过最小阈值

## 📈 使用示例

### 基本使用
```python
from enhanced_macd_trading import EnhancedMACDStrategy

# 创建策略实例
strategy = EnhancedMACDStrategy()

# 更新数据并生成信号
signal = strategy.update_strategy()

# 执行交易
if signal != 0:
    success = strategy.execute_trade(signal)
    print(f"交易执行结果: {success}")
```

### 自定义配置
```python
from macd_trading_config import get_full_config

# 获取默认配置
config = get_full_config()

# 修改配置
config['trading']['symbol'] = 'TSLA.US'
config['trading']['position_size'] = 2
config['macd']['fast_period'] = 8

# 使用自定义配置创建策略
strategy = EnhancedMACDStrategy(config)
```

## ⚠️ 重要提醒

### 风险警告
1. **实盘交易有风险**：本系统直接连接真实交易账户，请确保充分理解交易风险
2. **资金安全**：建议先用小额资金测试，确认系统稳定后再增加投入
3. **市场风险**：任何技术指标都不能保证盈利，请做好风险管理

### 使用建议
1. **充分测试**：在实盘使用前，建议先进行充分的回测和模拟交易
2. **监控运行**：系统运行时请保持监控，及时处理异常情况
3. **定期检查**：定期检查交易日志和账户状态
4. **参数优化**：根据市场情况和交易结果，适时调整策略参数

### 技术要求
1. **稳定网络**：确保网络连接稳定，避免交易中断
2. **系统稳定**：建议在稳定的服务器环境中运行
3. **API权限**：确保Longbridge API权限正常，有足够的调用额度

## 🔧 故障排除

### 常见问题

**1. API连接失败**
- 检查环境变量是否正确设置
- 确认API密钥是否有效
- 检查网络连接

**2. 数据获取失败**
- 确认股票代码格式正确
- 检查市场是否开放
- 验证API权限

**3. 交易执行失败**
- 检查账户余额是否足够
- 确认交易权限
- 验证订单参数

**4. 程序异常退出**
- 查看日志文件了解错误原因
- 检查配置参数是否合理
- 确认系统资源充足

## 📞 支持

如有问题或建议，请查看：
1. 日志文件中的错误信息
2. Longbridge API文档
3. 系统配置是否正确

## 📄 许可证

本项目仅供学习和研究使用，使用者需自行承担交易风险。
