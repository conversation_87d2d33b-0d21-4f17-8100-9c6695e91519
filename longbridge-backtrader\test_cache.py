"""
缓存功能测试脚本
===============

简单测试缓存功能是否正常工作的脚本。

运行前请确保：
1. 已设置LongBridge API环境变量
2. 已安装所需的Python包

作者: AI Assistant
版本: 1.0
"""

import os
import sys
from datetime import datetime

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from lB_BT_Plotly import BacktestSystem, LongBridgeData, DataCacheManager
    print("✓ 成功导入缓存功能模块")
except ImportError as e:
    print(f"✗ 导入模块失败: {e}")
    sys.exit(1)

def test_cache_manager():
    """测试缓存管理器基本功能"""
    print("\n=== 测试缓存管理器 ===")
    
    try:
        # 创建缓存管理器
        cache_manager = DataCacheManager("test_cache")
        print("✓ 缓存管理器创建成功")
        
        # 测试缓存信息
        info = cache_manager.get_cache_info()
        print(f"✓ 缓存信息获取成功: {info}")
        
        return True
    except Exception as e:
        print(f"✗ 缓存管理器测试失败: {e}")
        return False

def test_data_downloader():
    """测试数据下载器缓存功能"""
    print("\n=== 测试数据下载器缓存功能 ===")
    
    try:
        # 创建数据下载器（启用缓存）
        downloader = LongBridgeData(enable_cache=True, cache_dir="test_cache")
        print("✓ 数据下载器创建成功")
        
        # 测试缓存信息
        downloader.print_cache_info()
        print("✓ 缓存信息显示成功")
        
        return True
    except Exception as e:
        print(f"✗ 数据下载器测试失败: {e}")
        return False

def test_backtest_system():
    """测试回测系统缓存功能"""
    print("\n=== 测试回测系统缓存功能 ===")
    
    try:
        # 创建回测系统（启用缓存）
        system = BacktestSystem(enable_cache=True, cache_dir="test_cache")
        print("✓ 回测系统创建成功")
        
        # 测试缓存信息
        system.print_cache_info()
        print("✓ 缓存信息显示成功")
        
        return True
    except Exception as e:
        print(f"✗ 回测系统测试失败: {e}")
        return False

def test_with_mock_data():
    """使用模拟数据测试缓存功能"""
    print("\n=== 使用模拟数据测试缓存 ===")
    
    try:
        import pandas as pd
        from datetime import datetime, timedelta
        
        # 创建缓存管理器
        cache_manager = DataCacheManager("test_cache")
        
        # 创建模拟数据
        dates = pd.date_range(start='2023-01-01', end='2023-01-10', freq='D')
        mock_data = pd.DataFrame({
            'open': [100 + i for i in range(len(dates))],
            'high': [105 + i for i in range(len(dates))],
            'low': [95 + i for i in range(len(dates))],
            'close': [102 + i for i in range(len(dates))],
            'volume': [1000000 + i*10000 for i in range(len(dates))]
        }, index=dates)
        
        print(f"✓ 创建模拟数据: {mock_data.shape}")
        
        # 测试保存数据
        symbol = "TEST.US"
        start_date = datetime(2023, 1, 1)
        end_date = datetime(2023, 1, 10)
        
        success = cache_manager.save_data(symbol, start_date, end_date, mock_data)
        if success:
            print("✓ 数据保存成功")
        else:
            print("✗ 数据保存失败")
            return False
        
        # 测试读取数据
        loaded_data = cache_manager.load_data(symbol, start_date, end_date)
        if loaded_data is not None:
            print(f"✓ 数据读取成功: {loaded_data.shape}")
            
            # 验证数据一致性
            if loaded_data.equals(mock_data):
                print("✓ 数据一致性验证通过")
            else:
                print("✗ 数据一致性验证失败")
                return False
        else:
            print("✗ 数据读取失败")
            return False
        
        # 测试缓存信息
        info = cache_manager.get_cache_info()
        print(f"✓ 缓存信息: {info}")
        
        # 清理测试数据
        cache_manager.clear_cache(symbol)
        print("✓ 测试数据清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 模拟数据测试失败: {e}")
        return False

def cleanup_test_cache():
    """清理测试缓存"""
    print("\n=== 清理测试缓存 ===")
    
    try:
        cache_manager = DataCacheManager("test_cache")
        cache_manager.clear_cache()
        print("✓ 测试缓存清理完成")
        
        # 删除测试缓存目录
        import shutil
        if os.path.exists("test_cache"):
            shutil.rmtree("test_cache")
            print("✓ 测试缓存目录删除完成")
            
    except Exception as e:
        print(f"✗ 清理测试缓存失败: {e}")

def main():
    """主测试函数"""
    print("LongBridge回测系统缓存功能测试")
    print("="*50)
    
    tests = [
        ("缓存管理器基本功能", test_cache_manager),
        ("数据下载器缓存功能", test_data_downloader),
        ("回测系统缓存功能", test_backtest_system),
        ("模拟数据缓存测试", test_with_mock_data),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                print(f"✓ {test_name} 通过")
                passed += 1
            else:
                print(f"✗ {test_name} 失败")
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
    
    # 清理测试数据
    cleanup_test_cache()
    
    # 显示测试结果
    print(f"\n{'='*50}")
    print(f"测试结果: {passed}/{total} 通过")
    if passed == total:
        print("🎉 所有测试通过！缓存功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查配置。")
    print(f"{'='*50}")

if __name__ == "__main__":
    main()
