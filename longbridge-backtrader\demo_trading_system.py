"""
MACD交易系统演示版本
==================

在没有网络连接的情况下演示可配置周期功能
使用模拟数据进行策略测试
"""

import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
import logging

# 导入配置模块
from trading_period_config import TradingPeriodConfig, get_trading_template

class DemoMACDStrategy:
    """演示版MACD策略类"""
    
    def __init__(self, symbol="DEMO.US", period='1h'):
        self.symbol = symbol
        self.period = period
        self.period_info = TradingPeriodConfig.get_period_info(period)
        self.config = TradingPeriodConfig.get_recommended_config(period)
        
        # MACD参数
        self.fast_period = self.config['macd_config']['fast']
        self.slow_period = self.config['macd_config']['slow']
        self.signal_period = self.config['macd_config']['signal']
        
        # 滤波参数
        self.filter_window = self.config['filter_config']['window']
        
        # 设置日志
        self.logger = self._setup_logger()
        
        self.logger.info(f"演示策略初始化: {symbol}, 周期: {self.period_info['name']}")
    
    def _setup_logger(self):
        """设置日志"""
        logger = logging.getLogger(f"DemoMACD_{self.symbol}_{self.period}")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def generate_demo_data(self, days=30):
        """生成演示用的K线数据"""
        self.logger.info(f"生成{days}天的演示数据...")
        
        # 根据周期计算数据点数量
        seconds_per_day = 24 * 60 * 60
        period_seconds = self.period_info['seconds']
        points_per_day = seconds_per_day // period_seconds
        total_points = days * points_per_day
        
        # 生成时间序列
        end_time = datetime.now()
        start_time = end_time - timedelta(days=days)
        time_delta = timedelta(seconds=period_seconds)
        
        timestamps = []
        current_time = start_time
        while current_time <= end_time:
            timestamps.append(current_time)
            current_time += time_delta
        
        # 生成价格数据（模拟股价走势）
        np.random.seed(42)  # 固定随机种子，确保结果可重现
        
        base_price = 100.0
        prices = [base_price]
        
        for i in range(1, len(timestamps)):
            # 模拟价格随机游走，带有轻微趋势
            change = np.random.normal(0, 0.02)  # 2%的标准差
            trend = 0.0001 * i  # 轻微上升趋势
            new_price = prices[-1] * (1 + change + trend)
            prices.append(max(new_price, 1.0))  # 确保价格不为负
        
        # 生成OHLC数据
        data = []
        for i, (timestamp, close) in enumerate(zip(timestamps, prices)):
            # 模拟开高低收
            volatility = close * 0.01  # 1%的波动率
            high = close + np.random.uniform(0, volatility)
            low = close - np.random.uniform(0, volatility)
            open_price = prices[i-1] if i > 0 else close
            
            data.append({
                'timestamp': timestamp,
                'open': round(open_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'close': round(close, 2),
                'volume': np.random.randint(1000, 10000)
            })
        
        df = pd.DataFrame(data)
        self.logger.info(f"生成了{len(df)}个数据点")
        return df
    
    def calculate_macd(self, data):
        """计算MACD指标"""
        close_prices = data['close']
        
        # 计算EMA
        ema_fast = close_prices.ewm(span=self.fast_period).mean()
        ema_slow = close_prices.ewm(span=self.slow_period).mean()
        
        # 计算MACD线和信号线
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=self.signal_period).mean()
        
        # 计算直方图
        histogram = macd_line - signal_line
        
        return {
            'macd_line': macd_line,
            'signal_line': signal_line,
            'histogram': histogram
        }
    
    def apply_filter(self, histogram):
        """对直方图应用滤波"""
        return histogram.rolling(window=self.filter_window, center=True).mean()
    
    def calculate_signals(self, filtered_histogram):
        """计算交易信号"""
        # 计算差分
        diff = filtered_histogram.diff()
        
        # 检测符号变化
        signals = []
        positions = []
        
        for i in range(1, len(diff)):
            if pd.isna(diff.iloc[i-1]) or pd.isna(diff.iloc[i]):
                signals.append('hold')
                positions.append(0)
                continue
            
            prev_sign = 1 if diff.iloc[i-1] > 0 else -1 if diff.iloc[i-1] < 0 else 0
            curr_sign = 1 if diff.iloc[i] > 0 else -1 if diff.iloc[i] < 0 else 0
            
            if prev_sign <= 0 and curr_sign > 0:
                signals.append('buy')
                positions.append(1)
            elif prev_sign >= 0 and curr_sign < 0:
                signals.append('sell')
                positions.append(-1)
            else:
                signals.append('hold')
                positions.append(0)
        
        # 第一个信号设为hold
        signals.insert(0, 'hold')
        positions.insert(0, 0)
        
        return signals, positions
    
    def run_backtest(self, data):
        """运行回测"""
        self.logger.info("开始运行策略回测...")
        
        # 计算MACD
        macd_data = self.calculate_macd(data)
        
        # 应用滤波
        filtered_histogram = self.apply_filter(macd_data['histogram'])
        
        # 计算信号
        signals, positions = self.calculate_signals(filtered_histogram)
        
        # 添加到数据框
        data = data.copy()
        data['macd_line'] = macd_data['macd_line']
        data['signal_line'] = macd_data['signal_line']
        data['histogram'] = macd_data['histogram']
        data['filtered_histogram'] = filtered_histogram
        data['signal'] = signals
        data['position'] = positions
        
        return data
    
    def analyze_performance(self, backtest_data):
        """分析策略表现"""
        self.logger.info("分析策略表现...")
        
        # 计算收益
        backtest_data['returns'] = backtest_data['close'].pct_change()
        backtest_data['strategy_returns'] = backtest_data['returns'] * backtest_data['position'].shift(1)
        
        # 计算累计收益
        backtest_data['cumulative_returns'] = (1 + backtest_data['returns']).cumprod()
        backtest_data['cumulative_strategy_returns'] = (1 + backtest_data['strategy_returns']).cumprod()
        
        # 统计信息
        total_trades = len(backtest_data[backtest_data['signal'] != 'hold'])
        buy_signals = len(backtest_data[backtest_data['signal'] == 'buy'])
        sell_signals = len(backtest_data[backtest_data['signal'] == 'sell'])
        
        final_return = backtest_data['cumulative_strategy_returns'].iloc[-1] - 1
        benchmark_return = backtest_data['cumulative_returns'].iloc[-1] - 1
        
        self.logger.info(f"回测结果:")
        self.logger.info(f"  总交易次数: {total_trades}")
        self.logger.info(f"  买入信号: {buy_signals}")
        self.logger.info(f"  卖出信号: {sell_signals}")
        self.logger.info(f"  策略收益: {final_return:.2%}")
        self.logger.info(f"  基准收益: {benchmark_return:.2%}")
        self.logger.info(f"  超额收益: {final_return - benchmark_return:.2%}")
        
        return {
            'total_trades': total_trades,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'strategy_return': final_return,
            'benchmark_return': benchmark_return,
            'excess_return': final_return - benchmark_return
        }

def demo_different_periods():
    """演示不同周期的策略表现"""
    print("="*80)
    print("🎯 MACD交易系统 - 不同周期策略演示")
    print("="*80)
    
    periods_to_test = ['5m', '15m', '1h', '1d']
    results = {}
    
    for period in periods_to_test:
        print(f"\n📊 测试 {period} 周期策略...")
        
        # 创建策略
        strategy = DemoMACDStrategy(symbol="DEMO.US", period=period)
        
        # 生成数据
        data = strategy.generate_demo_data(days=30)
        
        # 运行回测
        backtest_data = strategy.run_backtest(data)
        
        # 分析表现
        performance = strategy.analyze_performance(backtest_data)
        results[period] = performance
        
        print(f"✅ {period} 周期测试完成")
    
    # 比较结果
    print("\n" + "="*80)
    print("📈 不同周期策略表现对比")
    print("="*80)
    
    print(f"{'周期':<8} {'交易次数':<10} {'策略收益':<12} {'基准收益':<12} {'超额收益':<12}")
    print("-" * 60)
    
    for period, result in results.items():
        period_info = TradingPeriodConfig.get_period_info(period)
        print(f"{period_info['name']:<8} "
              f"{result['total_trades']:<10} "
              f"{result['strategy_return']:<12.2%} "
              f"{result['benchmark_return']:<12.2%} "
              f"{result['excess_return']:<12.2%}")
    
    print("="*80)

def demo_trading_templates():
    """演示交易模板"""
    print("\n🎯 交易模板演示")
    print("="*50)
    
    templates = ['scalping', 'day_trading', 'swing_trading', 'position_trading']
    
    for template_name in templates:
        print(f"\n📋 {template_name} 模板:")
        
        # 获取模板配置
        template_config = get_trading_template(template_name)
        period = template_config['period']
        
        print(f"   名称: {template_config['name']}")
        print(f"   周期: {template_config['period']}")
        print(f"   风险等级: {template_config['risk_level']}")
        print(f"   更新间隔: {template_config['update_interval']}秒")
        
        # 创建策略并快速测试
        strategy = DemoMACDStrategy(symbol="DEMO.US", period=period)
        data = strategy.generate_demo_data(days=10)  # 较短的测试期
        backtest_data = strategy.run_backtest(data)
        performance = strategy.analyze_performance(backtest_data)
        
        print(f"   测试结果: 策略收益 {performance['strategy_return']:.2%}")

def main():
    """主演示函数"""
    print("🚀 MACD交易系统演示 - 可配置周期版本")
    
    try:
        # 演示不同周期
        demo_different_periods()
        
        # 演示交易模板
        demo_trading_templates()
        
        print("\n🎉 演示完成！")
        print("\n💡 说明:")
        print("- 这是使用模拟数据的演示版本")
        print("- 实际使用时需要连接Longbridge API获取真实数据")
        print("- 可配置周期功能已成功实现")
        print("- 不同周期的策略参数已自动优化")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
