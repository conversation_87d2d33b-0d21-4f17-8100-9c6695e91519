# LongBridge回测系统缓存功能说明（CSV格式）

## 概述

本系统新增了智能数据缓存功能，可以将从LongBridge API下载的历史数据保存到本地CSV文件，在后续回测中优先使用本地离线数据，大大提高回测效率并减少API调用。

## 主要特性

### ✨ 核心功能
- **自动缓存**: 首次下载数据时自动保存到本地CSV文件
- **智能读取**: 优先使用本地缓存，缓存不存在时才调用API
- **CSV格式**: 使用CSV格式存储，可直接用Excel等工具查看和编辑
- **元数据管理**: 记录缓存创建时间、数据量等信息
- **灵活控制**: 支持强制重新下载和缓存清理

### 🚀 性能优势
- **速度提升**: 缓存读取比API下载快5-10倍
- **减少API调用**: 避免重复下载相同数据
- **离线使用**: 无网络时也可进行回测
- **成本节约**: 减少API调用次数

### 📊 CSV格式优势
- **可读性强**: 可以用Excel、记事本等工具直接打开查看
- **通用性好**: 几乎所有数据分析工具都支持CSV格式
- **便于检查**: 可以直接查看数据内容，验证数据正确性
- **易于编辑**: 可以手动修改数据（如果需要）
- **跨平台**: 在Windows、Mac、Linux上都可以使用
- **版本控制**: 可以用Git等工具跟踪数据变化

## 使用方法

### 1. 基本使用

```python
from lB_BT_Plotly import BacktestSystem
from datetime import datetime

# 创建启用缓存的回测系统
system = BacktestSystem(
    enable_cache=True,        # 启用缓存
    cache_dir="data_cache"    # 缓存目录
)

# 运行回测（首次会下载并缓存数据）
results = system.run_backtest(
    symbol="AAPL.US",
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    initial_cash=100000
)

# 再次运行相同回测（会使用缓存数据）
results2 = system.run_backtest(
    symbol="AAPL.US",
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    initial_cash=100000
)
```

### 2. 强制重新下载

```python
# 忽略缓存，强制重新下载数据
results = system.run_backtest(
    symbol="AAPL.US",
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 12, 31),
    initial_cash=100000,
    force_download=True  # 强制重新下载
)
```

### 3. 缓存管理

```python
# 查看缓存信息
system.print_cache_info()

# 获取缓存统计
cache_info = system.get_cache_info()
print(f"缓存文件数: {cache_info['total_cached_files']}")
print(f"缓存大小: {cache_info['total_cache_size_mb']:.2f} MB")

# 清理特定股票的缓存
system.clear_cache("AAPL.US")

# 清理全部缓存
system.clear_cache()
```

### 4. 单独使用数据下载器

```python
from lB_BT_Plotly import LongBridgeData

# 创建数据下载器
downloader = LongBridgeData(
    enable_cache=True,
    cache_dir="my_cache"
)

# 下载数据（会自动缓存）
data = downloader.download_data(
    symbol="TSLA.US",
    start_date=datetime(2023, 1, 1),
    end_date=datetime(2023, 6, 30)
)

# 管理缓存
downloader.print_cache_info()
downloader.clear_cache("TSLA.US")
```

## 缓存机制详解

### 缓存文件结构
```
data_cache/
├── cache_metadata.json          # 缓存元数据
├── AAPL.US_20230101_20231231.csv  # 缓存数据文件（CSV格式）
├── TSLA.US_20230101_20230630.csv
└── ...
```

### 缓存键生成规则
- 格式: `{股票代码}_{开始日期}_{结束日期}`
- 示例: `AAPL.US_20230101_20231231`
- 文件扩展名: `.csv`

### CSV文件格式
```csv
datetime,open,high,low,close,volume
2023-01-01,150.25,152.80,149.50,151.75,89234567
2023-01-02,151.80,153.25,150.90,152.45,76543210
...
```

### 元数据信息
```json
{
  "AAPL.US_20230101_20231231": {
    "symbol": "AAPL.US",
    "start_date": "2023-01-01T00:00:00",
    "end_date": "2023-12-31T00:00:00",
    "cached_at": "2024-01-15T10:30:00",
    "data_points": 252,
    "file_size": 15360,
    "format": "csv"
  }
}
```

## 配置选项

### BacktestSystem参数
- `enable_cache`: 是否启用缓存 (默认: True)
- `cache_dir`: 缓存目录路径 (默认: "data_cache")

### LongBridgeData参数
- `enable_cache`: 是否启用缓存 (默认: True)
- `cache_dir`: 缓存目录路径 (默认: "data_cache")

### run_backtest参数
- `force_download`: 是否强制重新下载 (默认: False)

## 最佳实践

### 1. 缓存策略
- 对于历史数据回测，建议启用缓存
- 对于实时数据分析，可考虑禁用缓存
- 定期清理过期缓存以节省磁盘空间

### 2. 目录管理
- 为不同项目使用不同的缓存目录
- 定期备份重要的缓存数据
- 监控缓存目录大小

### 3. 性能优化
- 首次下载大量数据时可能较慢，后续会很快
- 批量下载多个股票时，缓存效果更明显
- 相同时间范围的数据只需下载一次

## 故障排除

### 常见问题

1. **缓存文件损坏**
   ```python
   # 清理损坏的缓存并重新下载
   system.clear_cache("AAPL.US")
   system.run_backtest(..., force_download=True)
   ```

2. **磁盘空间不足**
   ```python
   # 查看缓存大小
   system.print_cache_info()
   # 清理不需要的缓存
   system.clear_cache()
   ```

3. **权限问题**
   - 确保缓存目录有读写权限
   - 检查文件系统是否支持

### 调试技巧
- 查看控制台输出了解缓存状态
- 检查缓存目录是否正确创建
- 验证元数据文件是否正常

## 示例脚本

### CSV缓存功能演示
运行 `csv_cache_demo.py` 查看完整的CSV缓存功能演示：

```bash
python csv_cache_demo.py
```

该脚本包含：
- CSV缓存基本功能演示
- CSV缓存管理功能演示
- CSV文件检查功能演示
- CSV格式优势说明

### 测试脚本
运行 `test_csv_cache.py` 验证CSV缓存功能：

```bash
python test_csv_cache.py
```

### 查看CSV文件
缓存的CSV文件可以直接用以下工具打开：
- **Excel**: 双击CSV文件
- **记事本**: 右键 -> 打开方式 -> 记事本
- **Python**: `pd.read_csv('文件路径')`
- **任何文本编辑器**: VS Code、Notepad++等

## 注意事项

1. **数据一致性**: 缓存的数据是下载时的快照，不会自动更新
2. **存储空间**: 大量缓存会占用磁盘空间，需要定期清理
3. **API限制**: 缓存可以减少API调用，但不能完全避免
4. **版本兼容**: 升级系统时可能需要清理旧版本缓存

## 更新日志

- v1.0: 初始版本，支持基本缓存功能
- 后续版本将支持更多高级功能（如缓存过期、压缩等）
