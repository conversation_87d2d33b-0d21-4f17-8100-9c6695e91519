"""
MACD直方图量化交易策略
使用LongPort API实现基于MACD直方图变化的交易策略
"""

import os
import time
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Optional, Tuple
import logging

from longport.openapi import (
    QuoteContext, 
    TradeContext, 
    Config, 
    Period, 
    AdjustType,
    OrderSide, 
    OrderType, 
    TimeInForceType,
    PushOrderChanged
)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class MACDHistogramStrategy:
    """MACD直方图交易策略"""
    
    def __init__(self, symbol: str, max_position: int = 10, start_hour: int = 22):
        """
        初始化策略
        
        Args:
            symbol: 交易标的代码，如 "700.HK"
            max_position: 最大持仓数量（正负）
            start_hour: 交易开始时间（24小时制），如22表示晚上10点
        """
        self.symbol = symbol
        self.max_position = max_position
        self.start_hour = start_hour  # 交易开始时间
        self.current_position = 0  # 当前持仓
        
        # 初始化API连接
        self.config = Config.from_env()
        self.quote_ctx = QuoteContext(self.config)
        self.trade_ctx = TradeContext(self.config)
        
        # 设置订单状态推送回调
        self.trade_ctx.set_on_order_changed(self._on_order_changed)
        
        # MACD参数
        self.fast_period = 12
        self.slow_period = 26
        self.signal_period = 9
        
        logger.info(f"策略初始化完成 - 标的: {self.symbol}, 最大持仓: {self.max_position}")
        logger.info(f"交易时段: 每日{self.start_hour}点开始，每2小时为一个交易周期")
    
    def _on_order_changed(self, order_changed: PushOrderChanged):
        """订单状态变化回调"""
        logger.info(f"订单状态更新 - 订单ID: {order_changed.order_id}, "
                   f"状态: {order_changed.status}, "
                   f"已成交数量: {order_changed.executed_quantity}")
    
    def get_next_trading_time(self) -> datetime:
        """
        计算下一个交易时间点
        
        Returns:
            下一个交易时间（2小时周期的最后5分钟开始）
        """
        now = datetime.now()
        
        # 计算今天的开始交易时间
        today_start = now.replace(hour=self.start_hour, minute=0, second=0, microsecond=0)
        
        # 如果还没到今天的开始时间，就是今天的第一个交易时间
        if now < today_start:
            return today_start.replace(minute=55) - timedelta(hours=2)
        
        # 计算从开始时间到现在经过了多少个2小时周期
        hours_passed = (now - today_start).total_seconds() / 3600
        cycles_passed = int(hours_passed // 2)
        
        # 下一个交易时间是下一个2小时周期的倒数第5分钟
        next_cycle_start = today_start + timedelta(hours=(cycles_passed + 1) * 2)
        next_trading_time = next_cycle_start - timedelta(minutes=5)
        
        # 如果计算出的时间已经过去，则是下一个周期
        if next_trading_time <= now:
            next_trading_time = next_cycle_start + timedelta(hours=2) - timedelta(minutes=5)
        
        return next_trading_time
    
    def get_current_cycle_start(self) -> datetime:
        """
        获取当前2小时周期的开始时间
        
        Returns:
            当前周期开始时间
        """
        now = datetime.now()
        
        # 计算今天的开始交易时间
        today_start = now.replace(hour=self.start_hour, minute=0, second=0, microsecond=0)
        
        # 计算从开始时间到现在经过了多少个2小时周期
        if now >= today_start:
            hours_passed = (now - today_start).total_seconds() / 3600
            cycles_passed = int(hours_passed // 2)
            cycle_start = today_start + timedelta(hours=cycles_passed * 2)
        else:
            # 还在昨天的周期中
            yesterday_start = today_start - timedelta(days=1)
            hours_passed = (now - yesterday_start).total_seconds() / 3600
            cycles_passed = int(hours_passed // 2)
            cycle_start = yesterday_start + timedelta(hours=cycles_passed * 2)
        
        return cycle_start
    
    def get_historical_2hour_klines(self, count: int = 50) -> List[dict]:
        """
        获取历史2小时K线数据
        
        Args:
            count: 获取的2小时K线数量
            
        Returns:
            2小时K线数据列表
        """
        try:
            # 获取足够的小时K线数据
            hours_needed = count * 2 + 10  # 多获取一些以防万一
            
            # 获取1小时K线数据
            resp = self.quote_ctx.candlesticks(
                self.symbol,
                Period.Hour,
                hours_needed,
                AdjustType.NoAdjust
            )
            
            if not resp:
                logger.warning("无法获取历史K线数据")
                return []
            
            # 转换为字典格式
            hourly_klines = []
            for candle in resp:
                hourly_klines.append({
                    'timestamp': candle.timestamp,
                    'open': float(candle.open),
                    'high': float(candle.high),
                    'low': float(candle.low),
                    'close': float(candle.close),
                    'volume': candle.volume
                })
            
            # 合并为2小时K线
            two_hour_klines = self.merge_to_2hour_klines(hourly_klines)
            
            # 返回最近的count根
            return two_hour_klines[-count:] if len(two_hour_klines) > count else two_hour_klines
            
        except Exception as e:
            logger.error(f"获取历史K线数据失败: {e}")
            return []
    
    def merge_to_2hour_klines(self, hourly_klines: List[dict]) -> List[dict]:
        """
        将小时K线合并为2小时K线
        
        Args:
            hourly_klines: 小时K线数据
            
        Returns:
            2小时K线数据
        """
        two_hour_klines = []
        
        for i in range(0, len(hourly_klines) - 1, 2):  # 确保有完整的2小时数据
            # 合并两根小时K线
            kline1 = hourly_klines[i]
            kline2 = hourly_klines[i + 1]
            
            merged = {
                'timestamp': kline1['timestamp'],
                'open': kline1['open'],
                'high': max(kline1['high'], kline2['high']),
                'low': min(kline1['low'], kline2['low']),
                'close': kline2['close'],
                'volume': kline1['volume'] + kline2['volume']
            }
            two_hour_klines.append(merged)
        
        logger.info(f"合并后得到 {len(two_hour_klines)} 根2小时K线")
        return two_hour_klines
    
    def get_current_cycle_minute_klines(self) -> Optional[dict]:
        """
        获取当前2小时周期内的分钟K线数据并合并
        
        Returns:
            合并后的2小时K线（到当前时间前5分钟）
        """
        try:
            # 计算需要获取的分钟数
            cycle_start = self.get_current_cycle_start()
            now = datetime.now()
            minutes_passed = int((now - cycle_start).total_seconds() / 60)
            
            # 获取115分钟或实际经过的分钟数（取较小值）
            minutes_to_get = min(minutes_passed, 115)
            
            if minutes_to_get < 1:
                logger.warning("当前周期刚开始，没有足够的数据")
                return None
            
            logger.info(f"获取当前周期 {minutes_to_get} 分钟的K线数据")
            
            # 获取分钟K线
            resp = self.quote_ctx.candlesticks(
                self.symbol,
                Period.Min_1,
                minutes_to_get,
                AdjustType.NoAdjust
            )
            
            if not resp:
                return None
            
            # 合并为一根2小时K线
            opens = [float(candle.open) for candle in resp]
            highs = [float(candle.high) for candle in resp]
            lows = [float(candle.low) for candle in resp]
            closes = [float(candle.close) for candle in resp]
            volumes = [candle.volume for candle in resp]
            
            merged_kline = {
                'timestamp': resp[0].timestamp,
                'open': opens[0],
                'high': max(highs),
                'low': min(lows),
                'close': closes[-1],
                'volume': sum(volumes)
            }
            
            logger.info(f"合并当前周期K线完成 - O:{merged_kline['open']:.2f} "
                       f"H:{merged_kline['high']:.2f} L:{merged_kline['low']:.2f} "
                       f"C:{merged_kline['close']:.2f}")
            return merged_kline
            
        except Exception as e:
            logger.error(f"获取当前分钟K线失败: {e}")
            return None
    
    def calculate_macd(self, klines: List[dict]) -> Tuple[List[float], List[float], List[float]]:
        """
        计算MACD指标
        
        Args:
            klines: K线数据
            
        Returns:
            (MACD线, 信号线, 直方图)
        """
        if len(klines) < self.slow_period:
            logger.warning(f"K线数据不足，无法计算MACD")
            return [], [], []
        
        # 提取收盘价
        closes = np.array([k['close'] for k in klines])
        
        # 计算EMA
        def calculate_ema(data, period):
            ema = np.zeros_like(data)
            ema[0] = data[0]
            alpha = 2.0 / (period + 1)
            for i in range(1, len(data)):
                ema[i] = alpha * data[i] + (1 - alpha) * ema[i-1]
            return ema
        
        # 计算MACD线
        ema_fast = calculate_ema(closes, self.fast_period)
        ema_slow = calculate_ema(closes, self.slow_period)
        macd_line = ema_fast - ema_slow
        
        # 计算信号线
        signal_line = calculate_ema(macd_line, self.signal_period)
        
        # 计算直方图
        histogram = macd_line - signal_line
        
        return macd_line.tolist(), signal_line.tolist(), histogram.tolist()
    
    def analyze_histogram_trend(self, histogram: List[float]) -> str:
        """
        分析MACD直方图趋势（基于差分）
        
        Args:
            histogram: MACD直方图数据
            
        Returns:
            "shortening" - 直方图开始变短
            "lengthening" - 直方图开始变长
            "stable" - 无明显变化
        """
        if len(histogram) < 2:
            return "stable"
        
        # 计算最后两个直方图的绝对值差分
        current_abs = abs(histogram[-1])
        previous_abs = abs(histogram[-2])
        
        # 考虑直方图的正负和变化
        current = histogram[-1]
        previous = histogram[-2]
        
        # 判断趋势
        if current_abs < previous_abs:
            logger.info(f"直方图变短: {previous:.4f} -> {current:.4f} (绝对值: {previous_abs:.4f} -> {current_abs:.4f})")
            return "shortening"
        elif current_abs > previous_abs:
            logger.info(f"直方图变长: {previous:.4f} -> {current:.4f} (绝对值: {previous_abs:.4f} -> {current_abs:.4f})")
            return "lengthening"
        else:
            return "stable"
    
    def get_current_position(self) -> int:
        """
        获取当前持仓数量
        
        Returns:
            持仓数量（正数为多头，负数为空头）
        """
        try:
            # 获取持仓信息
            positions = self.trade_ctx.stock_positions()
            
            for pos in positions:
                if pos.symbol == self.symbol:
                    # 计算净持仓
                    quantity = int(pos.quantity)
                    self.current_position = quantity
                    logger.info(f"当前持仓: {self.current_position}")
                    return self.current_position
            
            self.current_position = 0
            logger.info(f"当前无持仓")
            return 0
            
        except Exception as e:
            logger.error(f"获取持仓失败: {e}")
            return self.current_position
    
    def place_order(self, side: OrderSide, quantity: int) -> Optional[str]:
        """
        下单
        
        Args:
            side: 买卖方向
            quantity: 数量
            
        Returns:
            订单ID
        """
        try:
            # 获取最新报价
            quote = self.quote_ctx.quote([self.symbol])[0]
            
            # 根据买卖方向确定价格（加减滑点）
            if side == OrderSide.Buy:
                price = Decimal(str(float(quote.ask_price) * 1.001))  # 买入加0.1%滑点
            else:
                price = Decimal(str(float(quote.bid_price) * 0.999))  # 卖出减0.1%滑点
            
            # 提交订单
            resp = self.trade_ctx.submit_order(
                side=side,
                symbol=self.symbol,
                order_type=OrderType.LO,  # 限价单
                submitted_price=price,
                submitted_quantity=Decimal(str(quantity)),
                time_in_force=TimeInForceType.Day,
                remark=f"MACD策略-{datetime.now()}"
            )
            
            logger.info(f"订单提交成功 - ID: {resp.order_id}, "
                       f"方向: {side}, 数量: {quantity}, 价格: {price}")
            return resp.order_id
            
        except Exception as e:
            logger.error(f"下单失败: {e}")
            return None
    
    def execute_trading_logic(self, histogram_trend: str):
        """
        执行交易逻辑
        
        Args:
            histogram_trend: 直方图趋势
        """
        # 获取当前持仓
        current_pos = self.get_current_position()
        
        if histogram_trend == "shortening":
            # 直方图开始变短 - 卖出信号
            target_position = -self.max_position
            
            if current_pos > target_position:
                # 计算需要卖出的数量
                sell_quantity = current_pos - target_position
                
                if sell_quantity > 0:
                    logger.info(f"执行卖出信号 - 当前持仓: {current_pos}, 目标持仓: {target_position}, 卖出数量: {sell_quantity}")
                    self.place_order(OrderSide.Sell, sell_quantity)
            else:
                logger.info(f"已达目标空头持仓 {target_position}，无需卖出")
        
        elif histogram_trend == "lengthening":
            # 直方图开始变长 - 买入信号
            target_position = self.max_position
            
            if current_pos < target_position:
                # 计算需要买入的数量
                buy_quantity = target_position - current_pos
                
                if buy_quantity > 0:
                    logger.info(f"执行买入信号 - 当前持仓: {current_pos}, 目标持仓: {target_position}, 买入数量: {buy_quantity}")
                    self.place_order(OrderSide.Buy, buy_quantity)
            else:
                logger.info(f"已达目标多头持仓 {target_position}，无需买入")
        
        else:
            logger.info("直方图无明显变化，不执行交易")
    
    def execute_trading_cycle(self):
        """
        执行一个交易周期
        """
        try:
            now = datetime.now()
            cycle_start = self.get_current_cycle_start()
            
            logger.info(f"="*60)
            logger.info(f"执行交易周期 - 当前时间: {now}")
            logger.info(f"当前周期开始时间: {cycle_start}")
            
            # 1. 获取历史50根2小时K线
            historical_klines = self.get_historical_2hour_klines(50)
            
            if not historical_klines:
                logger.warning("无法获取历史K线，跳过本轮")
                return False
            
            logger.info(f"获取到 {len(historical_klines)} 根历史2小时K线")
            
            # 2. 获取当前周期的分钟K线并合并
            current_kline = self.get_current_cycle_minute_klines()
            
            if not current_kline:
                logger.warning("无法获取当前周期K线，跳过本轮")
                return False
            
            # 3. 合并历史和当前K线（51根）
            all_klines = historical_klines + [current_kline]
            logger.info(f"总共 {len(all_klines)} 根K线用于计算MACD")
            
            # 4. 计算MACD
            macd_line, signal_line, histogram = self.calculate_macd(all_klines)
            
            if not histogram:
                logger.warning("无法计算MACD，跳过本轮")
                return False
            
            # 5. 输出MACD信息
            logger.info(f"MACD计算完成:")
            logger.info(f"  MACD线: {macd_line[-1]:.4f}")
            logger.info(f"  信号线: {signal_line[-1]:.4f}")
            logger.info(f"  直方图: {histogram[-1]:.4f}")
            
            # 6. 分析直方图趋势
            trend = self.analyze_histogram_trend(histogram)
            logger.info(f"直方图趋势判断: {trend}")
            
            # 7. 执行交易逻辑
            self.execute_trading_logic(trend)
            
            logger.info(f"交易周期执行完成")
            logger.info(f"="*60)
            return True
            
        except Exception as e:
            logger.error(f"执行交易周期出错: {e}")
            return False
    
    def run_strategy(self):
        """
        运行策略主循环
        """
        logger.info("策略开始运行...")
        logger.info(f"交易时间设置: 每天{self.start_hour}点开始，每2小时一个周期")
        logger.info(f"交易执行时间: 每个周期的最后5分钟（XX:55-XX:00）")
        
        while True:
            try:
                # 获取下一个交易时间
                next_trading_time = self.get_next_trading_time()
                now = datetime.now()
                
                # 计算等待时间
                wait_seconds = (next_trading_time - now).total_seconds()
                
                if wait_seconds > 0:
                    wait_minutes = int(wait_seconds / 60)
                    logger.info(f"当前时间: {now.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"下次交易时间: {next_trading_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    logger.info(f"等待 {wait_minutes} 分钟...")
                    
                    # 如果等待时间超过5分钟，则分段等待
                    while wait_seconds > 300:  # 5分钟
                        time.sleep(300)
                        wait_seconds = (next_trading_time - datetime.now()).total_seconds()
                        remaining_minutes = int(wait_seconds / 60)
                        if remaining_minutes > 0:
                            logger.info(f"还需等待 {remaining_minutes} 分钟...")
                    
                    # 等待剩余时间
                    if wait_seconds > 0:
                        time.sleep(wait_seconds)
                
                # 执行交易
                logger.info(f"到达交易时间，开始执行交易逻辑...")
                success = self.execute_trading_cycle()
                
                if success:
                    # 交易时间窗口内持续监控（5分钟）
                    logger.info("进入5分钟交易窗口，监控订单执行...")
                    time.sleep(300)  # 等待5分钟
                else:
                    # 如果执行失败，等待1分钟后重试
                    time.sleep(60)
                    
            except KeyboardInterrupt:
                logger.info("策略被用户中断")
                break
            except Exception as e:
                logger.error(f"策略执行出错: {e}")
                time.sleep(60)  # 出错后等待1分钟再继续
    
    def test_time_logic(self):
        """
        测试时间逻辑是否正确
        """
        logger.info("测试时间逻辑...")
        logger.info(f"交易开始时间: 每天{self.start_hour}点")
        
        # 模拟不同时间点
        test_times = [
            datetime.now().replace(hour=21, minute=30),  # 21:30
            datetime.now().replace(hour=21, minute=55),  # 21:55 - 第一个交易时间
            datetime.now().replace(hour=22, minute=0),   # 22:00 - 第一个周期开始
            datetime.now().replace(hour=23, minute=55),  # 23:55 - 第一个交易时间
            datetime.now().replace(hour=0, minute=0),    # 00:00 - 第二个周期开始
            datetime.now().replace(hour=1, minute=55),   # 01:55 - 第二个交易时间
            datetime.now().replace(hour=2, minute=0),    # 02:00 - 第三个周期开始
        ]
        
        for test_time in test_times:
            # 临时替换当前时间（仅用于测试）
            logger.info(f"\n测试时间: {test_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 计算当前周期
            cycle_start = self.get_current_cycle_start()
            logger.info(f"  当前周期开始: {cycle_start.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 计算下一个交易时间
            next_time = self.get_next_trading_time()
            logger.info(f"  下次交易时间: {next_time.strftime('%Y-%m-%d %H:%M:%S')}")


def main():
    """主函数"""
    # 配置参数
    SYMBOL = os.getenv("TRADING_SYMBOL", "YINN.US")  # 默认腾讯
    MAX_POSITION = int(os.getenv("MAX_POSITION", "10"))  # 最大持仓
    START_HOUR = int(os.getenv("START_HOUR", "22"))  # 交易开始时间（默认晚上10点）
    MODE = os.getenv("TRADING_MODE", "backtest")  # 运行模式: test, backtest 或 live
    
    # 创建策略实例
    strategy = MACDHistogramStrategy(
        symbol=SYMBOL, 
        max_position=MAX_POSITION,
        start_hour=START_HOUR
    )
    
    if MODE == "test":
        # 测试时间逻辑
        strategy.test_time_logic()
    elif MODE == "backtest":
        # 回测模式 - 执行一次交易周期作为演示
        logger.info("回测模式 - 执行一次交易周期")
        strategy.execute_trading_cycle()
    else:
        # 实盘模式
        logger.warning("即将开始实盘交易，请确认已充分测试！")
        logger.warning("5秒后开始执行...")
        time.sleep(5)  # 给用户5秒时间取消
        strategy.run_strategy()


if __name__ == "__main__":
    main()