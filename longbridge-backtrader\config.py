"""
配置文件
========

用于管理回测系统的各种配置选项，包括伪装模式设置。
"""

# 伪装模式配置
# 设置为 True 启用伪装模式，将金融术语替换为频率/信号处理术语
# 设置为 False 使用正常的金融术语
DISGUISE_MODE = False

# 缓存配置
ENABLE_CACHE = True
CACHE_DIR = "data_cache"

# 默认回测参数
DEFAULT_INITIAL_CASH = 100000
DEFAULT_COMMISSION = 0.001  # 0.1% 手续费

# 伪装术语映射表（可自定义）
DISGUISE_TERMS = {
    # 基本术语
    '价格': '频率',
    '收盘': '采样',
    '开盘': '初始',
    '最高': '峰值',
    '最低': '谷值',
    '成交量': '数据量',
    '买入': '增强',
    '卖出': '衰减',
    '信号': '响应',
    '策略': '滤波器',
    '回测': '频响分析',
    '交易': '调制',
    
    # MACD相关
    'MACD': '频差',
    'Signal': '基准',
    '直方图': '差分谱',
    '金叉': '正交叉',
    '死叉': '负交叉',
    
    # 统计术语
    '总收益率': '总增益',
    '夏普比率': '信噪比',
    '最大回撤': '最大衰减',
    '胜率': '有效率',
    '盈利': '增益',
    '亏损': '损耗',
    '利润': '净增益',
    
    # 单位和符号
    '$': 'Hz',
    '%': 'dB',
    '日期': '时间点',
    '交易日': '采样点',
    '股票代码': '信号代码',
    '初始资金': '初始功率',
    '最终资金': '最终功率'
}

# 图表配置
CHART_CONFIG = {
    'figsize': (1600, 1000),
    'theme': 'plotly_white',
    'fullscreen': True
}

# MACD策略参数
MACD_PARAMS = {
    'fast_period': 12,
    'slow_period': 26,
    'signal_period': 9,
    'printlog': True
}

# 示例股票列表
EXAMPLE_SYMBOLS = [
    "AAPL.US",    # 苹果
    "TSLA.US",    # 特斯拉
    "00700.HK",   # 腾讯
    "YINN.US",    # 中国ETF
]

def get_disguise_config():
    """
    获取伪装模式配置
    
    Returns:
        dict: 包含伪装模式相关配置的字典
    """
    return {
        'enabled': DISGUISE_MODE,
        'terms': DISGUISE_TERMS
    }

def get_backtest_config():
    """
    获取回测配置
    
    Returns:
        dict: 包含回测相关配置的字典
    """
    return {
        'initial_cash': DEFAULT_INITIAL_CASH,
        'commission': DEFAULT_COMMISSION,
        'enable_cache': ENABLE_CACHE,
        'cache_dir': CACHE_DIR,
        'disguise_mode': DISGUISE_MODE
    }

def get_chart_config():
    """
    获取图表配置
    
    Returns:
        dict: 包含图表相关配置的字典
    """
    config = CHART_CONFIG.copy()
    config['disguise_mode'] = DISGUISE_MODE
    return config

def get_macd_config():
    """
    获取MACD策略配置
    
    Returns:
        dict: 包含MACD策略参数的字典
    """
    return MACD_PARAMS.copy()

# 使用示例：
# from config import DISGUISE_MODE, get_backtest_config
# 
# # 创建回测系统时使用配置
# config = get_backtest_config()
# system = BacktestSystem(**config)
